import json
import os
import random
import threading
import weeeTest
from datetime import datetime, timedelta
from common.api.order import OrderAPI
from weeeTest import log


class OrderTestUtils:
    """订单测试工具类，提供创建测试订单的便捷方法"""

    # 类级别的锁，确保线程安全
    _lock = threading.Lock()
    # 已使用的地址ID集合
    _used_address_ids = set()
    # 地址配置缓存
    _addresses_cache = None

    @classmethod
    def _load_addresses(cls):
        """加载地址配置文件"""
        if cls._addresses_cache is not None:
            return cls._addresses_cache

        # 获取配置文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(current_dir, '..', 'test_data', 'addresses.json')
        config_path = os.path.normpath(config_path)

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                cls._addresses_cache = config_data.get('addresses', [])
                log.info(f"成功加载 {len(cls._addresses_cache)} 个地址配置")
                return cls._addresses_cache
        except FileNotFoundError:
            raise FileNotFoundError(f"地址配置文件未找到: {config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"地址配置文件格式错误: {e}")
        except Exception as e:
            raise Exception(f"加载地址配置文件失败: {e}")

    @classmethod
    def _get_available_address(cls):
        """获取一个可用的地址，确保不重复使用"""
        with cls._lock:
            addresses = cls._load_addresses()

            # 获取所有地址ID
            all_address_ids = {addr['id'] for addr in addresses}

            # 计算可用的地址ID
            available_ids = all_address_ids - cls._used_address_ids

            if not available_ids:
                raise Exception(
                    f"所有地址已用尽！总共 {len(all_address_ids)} 个地址，"
                    f"已使用 {len(cls._used_address_ids)} 个地址。"
                    f"已使用的地址ID: {sorted(cls._used_address_ids)}"
                )

            # 随机选择一个可用地址
            selected_id = random.choice(list(available_ids))
            cls._used_address_ids.add(selected_id)

            # 找到对应的地址配置
            selected_address = next(addr for addr in addresses if addr['id'] == selected_id)

            log.info(f"选择地址: {selected_id} ({selected_address['addrCity']}, {selected_address['addrState']})")
            log.info(f"剩余可用地址数量: {len(available_ids) - 1}")

            return selected_address

    @classmethod
    def create_test_order(cls, products, user_id=7226349, delivery_date=None,
                         language="zh", platform="ios", is_points_pay=True):
        """
        创建测试订单的便捷方法

        Args:
            products (list): 商品列表，格式 [{"product_id": xxx, "quantity": xxx}, ...]
            user_id (int): 用户ID，默认 7226349
            delivery_date (str): 配送日期，格式 YYYY-MM-DD，默认为后天
            language (str): 语言，默认中文
            platform (str): 平台，默认ios
            is_points_pay (bool): 是否使用积分支付，默认True

        Returns:
            int: 订单ID

        Raises:
            Exception: 当所有地址都已使用或订单创建失败时
        """
        try:
            # 参数验证
            if not products or not isinstance(products, list):
                raise ValueError("products 参数必须是非空列表")

            for product in products:
                if not isinstance(product, dict) or 'product_id' not in product:
                    raise ValueError("products 列表中每个元素必须是包含 product_id 的字典")

            # 获取可用地址
            address_config = cls._get_available_address()

            # 构建地址信息（排除 id, phone, email）
            address_info = {
                "addrApt": address_config.get("addrApt", ""),
                "addrAddress": address_config.get("addrAddress", ""),
                "addrCity": address_config.get("addrCity", ""),
                "addrCountry": address_config.get("addrCountry", "2"),
                "addrFirstname": address_config.get("addrFirstname", ""),
                "addrLastname": address_config.get("addrLastname", ""),
                "addrState": address_config.get("addrState", ""),
                "addrZipcode": address_config.get("addrZipcode", "")
            }

            # 创建订单API实例
            order_api = OrderAPI()

            # 调用创建订单接口
            response = order_api.create_order(
                user_id=user_id,
                products=products,
                delivery_date=delivery_date,
                address_info=address_info,
                email=address_config.get("email"),
                phone=address_config.get("phone"),
                language=language,
                platform=platform,
                is_points_pay=is_points_pay
            )

            # 检查响应结果
            if not response.get("result"):
                error_msg = response.get("message", "未知错误")
                raise Exception(f"订单创建失败: {error_msg}, 完整响应: {response}")

            # 提取订单ID
            order_object = response.get("object")
            if not order_object:
                raise Exception(f"订单创建响应中缺少 object 字段: {response}")

            # 检查是否有失败的订单
            failed_orders = order_object.get("failed", {})
            if failed_orders:
                raise Exception(f"订单创建失败: {failed_orders}")

            # 从 success 字段中提取订单ID
            success_orders = order_object.get("success", {})
            if not success_orders:
                raise Exception(f"没有成功创建的订单，object 内容: {order_object}")

            # 获取第一个（通常也是唯一一个）订单ID
            # success 格式: {"1": "42659243"}
            order_id = list(success_orders.values())[0]

            if not order_id:
                raise Exception(f"订单ID为空，success 内容: {success_orders}")

            log.info(f"成功创建测试订单，订单ID: {order_id}, 使用地址: {address_config['id']}")
            return order_id

        except Exception as e:
            log.error(f"创建测试订单失败: {str(e)}")
            raise

    @classmethod
    def reset_used_addresses(cls):
        """重置已使用的地址列表（主要用于测试环境）"""
        with cls._lock:
            cls._used_address_ids.clear()
            log.info("已重置地址使用状态")

    @classmethod
    def get_address_usage_info(cls):
        """获取地址使用情况信息"""
        addresses = cls._load_addresses()
        total_count = len(addresses)
        used_count = len(cls._used_address_ids)
        available_count = total_count - used_count

        return {
            "total_addresses": total_count,
            "used_addresses": used_count,
            "available_addresses": available_count,
            "used_address_ids": sorted(list(cls._used_address_ids))
        }
