import weeeTest
from datetime import datetime, timedelta
from common.api.ec_auth import ECAuth
from weeeTest import log

class OrderAPI(weeeTest.TestCase):
    def __init__(self):
        super().__init__()
        self.ec_auth = ECAuth()
        self.ec_email = "<EMAIL>"
        self.ec_password = "1234abcd"
        # self.device_id = "2D1DFC5C-CE51-4E79-969E-014FA8753C65"  # 添加设备ID
        # 初始化时获取token
        self.refresh_token()

    def refresh_token(self):
        """刷新EC认证token"""
        self.ec_header = self.ec_auth.get_ec_auth_header(
            email=self.ec_email,
            password=self.ec_password
            # device_id=self.device_id  # 传递设备ID
        )
        if not self.ec_header:
            log.error("获取EC认证token失败")
        return self.ec_header

    def create_order(self, user_id=7226349, products=None, delivery_date=None, address_info=None, email=None, phone=None, language="zh", platform="ios", is_points_pay=True):
        """
        手动创建订单
        Args:
            user_id (int): 用户ID
            products (list): 商品列表，格式 [{"product_id": xxx, "quantity": xxx}, ...]
            delivery_date (str): 配送日期，格式 YYYY-MM-DD，默认为后天
            address_info (dict): 收货地址信息
            email (str): 用户邮箱
            phone (str): 用户电话
            language (str): 语言，默认中文
            platform (str): 平台，默认ios
            is_points_pay (bool): 是否使用积分支付
        Returns:
            dict: API响应结果
        """
        if delivery_date is None:
            delivery_date = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")

        # 默认地址信息
        default_address = {
            "addrApt": "15b",
            "addrAddress": "388 Pearl Street",
            "addrCity": "New York",
            "addrCountry": "2",
            "addrFirstname": "Alyssa",
            "addrLastname": "Myers",
            "addrState": "New York",
            "addrZipcode": "10038"
        }

        # 使用提供的地址信息覆盖默认值
        if address_info:
            default_address.update(address_info)

        # 构建商品列表
        order_products = []
        if products:
            for product in products:
                order_products.append({
                    "inventory_mode": "available",
                    "product_id": product.get("product_id"),
                    "quantity": product.get("quantity", 1),
                    "sort": 0
                })
        else:
            # 默认商品
            order_products = [
                {
                    "inventory_mode": "available",
                    "product_id": 48464,
                    "quantity": 1,
                    "sort": 0
                }
            ]

        # 构建完整的请求体
        body = [
            {
                "request_id": 1,
                "channel": "weee",
                "is_points_pay": is_points_pay,
                "gbOrder": {
                    **default_address,
                    "phone": phone or "6502425661",
                    "platform": platform,
                    "languange": language,
                    "email": email or "<EMAIL>",
                    "gbOrderExt": {
                        "shippingShipmentDate": delivery_date
                    },
                    "shipping": 0.00,
                    "orderSaleProducts": order_products,
                    "recCreatorId": user_id
                }
            }
        ]

        # 使用类成员变量中的EC认证header
        self.post(url='/ec/so/admin/manual/create/order', json=body, headers=self.ec_header)

        # 如果返回401未授权，可能是token过期，尝试刷新token后重试
        if self.response.get("code") == 401 or self.response.get("result") is False:
            log.info("Token可能已过期，尝试刷新token")
            self.refresh_token()
            self.post(url='/ec/so/admin/manual/create/order', json=body, headers=self.ec_header)

        return self.response
