import weeeTest
from erp.test_dir.api.merch.repack.repack import RepackManagement
from erp.test_dir.db_utils import DBConnect


class TestRepack(weeeTest.TestCase):

    @weeeTest.params.file(file_name="repack.yaml", key="repack_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_query_repack_list(self, request):
        """repack列表查询"""
        resp = RepackManagement().get_repack_list(request["params_data"])
        assert resp["result"] is True
        source_product_id = str(request["params_data"]["filter[source_product_id]"])
        sql = f"SELECT * FROM weee_comm.gb_product_repack WHERE source_product_id = {source_product_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        active_count = []
        active_record = []
        if len(db_res) == 0:
            assert resp["object"]["total_count"] == 0
        else:
            for x in range(len(db_res)):
                active_count.append(db_res[x][5])
                if db_res[x][5] == 'A':
                    active_record.append(db_res[x])
            if active_count.count("X") == len(db_res):
                assert resp["object"]["total_count"] == 0
            else:
                assert resp["object"]["total_count"] == 1
                assert int(resp["object"]["list"][0]["id"]) == active_record[0][0]
                assert int(resp["object"]["list"][0]["target_product_id"]) == active_record[0][2]
                assert int(resp["object"]["list"][0]["source_product_quantity"]) == active_record[0][3]
                assert int(resp["object"]["list"][0]["target_product_quantity"]) == active_record[0][4]
                assert int(resp["object"]["list"][0]["rec_creator_id"]) == active_record[0][6]

    @weeeTest.params.file(file_name="repack.yaml", key="update_repack_success")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_update_repack_list_success(self, request):
        """repack更新成功"""
        resp = RepackManagement().create_update_repack(request["json_data"])
        assert resp["result"] is True
        assert resp["object"]["product_repack_id"] == request["json_data"]["id"]
        sql = f"SELECT * FROM weee_comm.gb_product_repack WHERE id = {request['json_data']['id']}"
        db_res = DBConnect().select_data_from_mysql(sql)
        detail_resp = RepackManagement().get_repack_detail(request["json_data"]["id"])
        assert request["json_data"]["repack_inventory_ids"] == detail_resp["object"]["repack_inventory_ids"]
        sql_not_repack = f"SELECT * FROM weee_comm.gb_product_not_repack_config WHERE product_id = {request['json_data']['source_product_id']}"
        db_not_repack_resp = DBConnect().select_data_from_mysql(sql_not_repack)
        for x in range(len(detail_resp["object"]["repack_inventory_ids"])):
            for y in range(len(db_not_repack_resp)):
                assert int(detail_resp["object"]["repack_inventory_ids"][x]) not in db_not_repack_resp[y]
        assert request["json_data"]["source_product_quantity"] == detail_resp["object"]["source_product_quantity"]
        assert int(detail_resp["object"]["source_product_quantity"]) == db_res[0][3]
        assert request["json_data"]["target_product_quantity"] == detail_resp["object"]["target_product_quantity"]
        assert int(detail_resp["object"]["target_product_quantity"]) == db_res[0][4]

    @weeeTest.params.file(file_name="repack.yaml", key="update_repack_fail")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_update_repack_list_fail(self, request):
        """repack更新失败"""
        sql = f"SELECT * FROM weee_comm.gb_product_repack WHERE id ={request['json_data']['id']}"
        db_res_before = DBConnect().select_data_from_mysql(sql)
        detail_resp_before = RepackManagement().get_repack_detail(request["json_data"]["id"])
        resp = RepackManagement().create_update_repack(request["json_data"])
        assert resp["result"] is False
        assert request["message"] in resp["message"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after
        detail_resp_after = RepackManagement().get_repack_detail(request["json_data"]["id"])
        assert detail_resp_before == detail_resp_after

    @weeeTest.params.file(file_name="repack.yaml", key="delete_repack")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delete_repack(self, request):
        """repack删除"""
        resp = RepackManagement().get_repack_list(request["params_data"])
        assert resp["result"] is True
        if len(resp["object"]["list"]) == 0:
            add_resp = RepackManagement().create_update_repack(request["json_data"])
            assert add_resp["result"] is True
            repack_id = add_resp["object"]["product_repack_id"]
        else:
            repack_id = resp["object"]["list"][0]["id"]
        del_resp = RepackManagement().delete_repack(str(repack_id))
        assert del_resp["result"] is True
        sql = f"SELECT * FROM weee_comm.gb_product_repack WHERE id ={str(repack_id)}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][5] == "X"
        list_resp = RepackManagement().get_repack_list(request["params_data"])
        assert len(list_resp["object"]["list"]) == 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
