# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_delivery.py
@Description    :
@CreateTime     :  2023/7/19 17:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/19 17:30
"""
import datetime
import time

from weeeTest import log

from config.basic_db import ConnectDatabase
from config.secret import get_secret


class TmsDB:
    """
    TMS相关sql查询方法封装
    """
    db_config = get_secret()

    def __init__(self):
        self.cd = ConnectDatabase(host='weee.db.tb1.sayweee.net', user=self.db_config['db_wms_username'],
                                  password=self.db_config['db_wms_password'], db_name='tms')

    def get_user_id(self, name):
        """
        获取账号ID
        @param name: 账户名
        @return:
        """
        if '@' in name:
            name = name.split('@sayweee.com')[0]
        try:
            sql = f"""SELECT driver_user_id FROM tms.dm_driver_account WHERE email = '{name}@sayweee.com'"""
            results = self.cd.query_data(sql)
            log.info(f'请求参数:{name};请求结果:账号ID:{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def get_driver_org_id(self, user_id):
        """
        获取司机账户的sales_org_id
        @param user_id: 账户ID
        @return:
        """
        sql = f"""SELECT sales_org_id  FROM vm_driver p WHERE p.driver_user_id = {user_id} """
        try:
            results = self.cd.query_data(sql)
            log.info(f'司机ID:{user_id}的sales_org_id:{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def get_driver_sub_region_id(self, user_id):
        """
        获取司机账户的sub_region_id
        @param user_id: 账户ID
        @return:
        """
        sql = f"""SELECT sub_region_id  FROM vm_driver p WHERE p.driver_user_id = {user_id} """
        try:
            results = self.cd.query_data(sql)
            log.info(f'司机ID:{user_id}的sales_org_id:{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def get_driver_account_info(self, user_id, info='*'):
        """
        获取司机账户的信息
        @param user_id: 账户ID
        @param info: 查询字段
        @return:
        """
        sql = f"""SELECT {info}  FROM vm_driver p WHERE p.driver_user_id = {user_id} """
        try:
            results = self.cd.query_data(sql)
            log.info(f'司机ID:{user_id}的{info}信息为:{results[0]}')
            return results[0]
        except TypeError:
            return False

    def get_inventory_id(self, sales_org_id=1):
        """
        获取仓库ID
        @param sales_org_id: 销售组织ID
        @return:
        """
        sql = f"""SELECT pool_inventory_id FROM weee_comm.gb_sales_org p WHERE p.id = {sales_org_id}"""
        try:
            results = self.cd.query_data(sql)
            log.info(f'销售组织ID:{sales_org_id}对应的仓库ID:{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def get_feedback_status(self, rec_id):
        """
        获取反馈记录的状态
        @param rec_id: 反馈ID
        @return:
        """
        sql = f"""SELECT status FROM tms.tms_driver_feedback WHERE rec_id = {rec_id};"""
        try:
            results = self.cd.query_data(sql)
            log.info(f'Feedback ID为{rec_id}的反馈状态为:{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def get_delivery_plan_id(self, inventory_id=7, delivery_type="delivery", delivery_date="2022-07-25", status="C"):
        """
        获取派车计划ID
        @param inventory_id: 库存ID
        @param delivery_type: 派车单类型
        @param delivery_date: 配送日期
        @param status: 计划状态
        @return:
        """
        sql = f"""SELECT id FROM vm_delivery_plan WHERE inventory_id = {inventory_id}
        AND `status` = "{status}" 
        AND delivery_type = "{delivery_type}" 
        AND delivery_date = "{delivery_date}" ORDER BY id DESC LIMIT 1"""
        try:
            results = self.cd.query_data(sql)
            log.info(
                f'条件{inventory_id, delivery_type, delivery_date, status}下的派车计划ID:{results[0][0]}')
            return results[0][0]
        except TypeError:
            return False

    def get_dispatch_id(self, delivery_plan_id, flex_flag=None):
        """
        获取派车单ID
        :param delivery_plan_id:
        :param flex_flag:
        :return:
        """
        if flex_flag is not None:
            sql = f"""select id FROM  vm_dispatch  where delivery_plan_id = {delivery_plan_id} AND status = "A" AND flex_flag ={flex_flag} LIMIT 1"""
        else:
            sql = f"""select id FROM  vm_dispatch  where delivery_plan_id = {delivery_plan_id} AND status = "A" LIMIT 1"""
        results = self.cd.query_data(sql)
        if results:
            log.info(f'该派车计划ID:{delivery_plan_id}下的一个派车单ID为:{results[0][0]}')
            return results[0][0]
        else:
            log.info(f"该派车计划ID:{delivery_plan_id}下无派车单")
            return False

    def update_dispatch_status(self, dispatch_id=396156, delivery_date="2022-08-25", status='load_finish'):
        """
        更新派车单状态
        @param status: 派车单状态
        @param dispatch_id: 派车单ID
        @param delivery_date: 配送日期
        @return:
        """

        sql = f"""UPDATE `tms`.`vm_dispatch` SET
            `route_status` = '{status}', 
            `delivery_date` = "{delivery_date}", 
            `actual_start_time` = 0,
            `loading_start_time` = NULL,
            `loading_finish_time` = NULL,
            `route_check_time` = 1672193845,
            `out_time` = NULL, `actual_finish_time` = 0 WHERE `id` = {dispatch_id}"""
        self.cd.update_data(sql)
        log.info(f"更新派车单{dispatch_id}状态为:{status},配送时间为:{delivery_date}")

    def get_cutoff_time(self, delivery_time: int):
        """

        :param delivery_time: 时间戳
        :return:
        """
        sql = f"""SELECT  count( id ), last_cut_off_time from weee_comm.fpo_delivery_config fdc 
        where last_cut_off_time BETWEEN {delivery_time - 86400} AND {delivery_time} GROUP BY last_cut_off_time;"""
        results = self.cd.query_data(sql)
        if results:
            cutoff_time_list = [x[1] for x in results]
            log.info(f"获取的cutoff_time为{cutoff_time_list}")
            return cutoff_time_list
        else:
            log.info(f"获取的cutoff_time失败")
            return False

    def delete_task(self, dispatch_id):
        """
        删除派车单task任务
        @param dispatch_id: 派车单ID
        @return:
        """
        sql = f"""DELETE FROM `tms`.`tms_dispatch_task` WHERE `dispatch_id` = {dispatch_id}"""
        self.cd.update_data(sql)
        log.info(f"删除派车单{dispatch_id}下task任务")

    def update_task_statu(self, dispatch_id, task_seq, task_status=30, task_type='stop'):
        """
        更新派车单状态
        :param dispatch_id: 派车单ID
        :param task_type: task类型, inventory, stop
        :param task_seq: task序号, 即站点号
        :param task_status: task状态
        :return:
        """
        status_info = {
            10: 'pend',
            20: 'travel',
            30: 'finish',
            40: 'cancel',
            60: 'failure',
            70: 'partially_finish'
        }
        sql = f"""UPDATE `tms`.`tms_dispatch_task` SET `task_status` = {task_status}, task_step = '{status_info[task_status]}' WHERE `dispatch_id` = {dispatch_id} AND `task_type` = '{task_type}' AND `task_seq` = {task_seq}"""
        self.cd.update_data(sql)
        log.info(f"更新派车单{dispatch_id}下第{task_seq}个{task_type}任务状态为{status_info[task_status]}")

    def delete_driver_activity_log(self, driver_user_id):
        """
        删除司机抢单日志
        @param driver_user_id: 司机ID
        @return:
        """
        sql = f"""DELETE FROM tms.tms_driver_activity WHERE driver_user_id ={driver_user_id}"""
        self.cd.update_data(sql)
        log.info(f"删除司机{driver_user_id}的抢单日志")

    def check_task_created(self, dispatch_id):
        """
        校验派车单task任务是否生成
        @param dispatch_id: 派车单ID
        @return:
        """
        sql = f"""select task_id FROM `tms`.`tms_dispatch_task` WHERE `dispatch_id` = {dispatch_id}"""
        results = self.cd.query_data(sql)
        if results:
            log.info(f"派车单{dispatch_id}task任务存在")
            return results
        else:
            log.info(f"派车单{dispatch_id}task任务不存在")
            return False

    def check_delivery_plan_data(self, delivery_plan_id):
        """
        检查并初始化异常Delivery Plan数据
        :param delivery_plan_id:
        :return:
        """
        sql1 = f'SELECT status FROM tms.vm_delivery_plan where id = {delivery_plan_id}'
        sql2 = f'SELECT status FROM tms.tms_delivery_plan_draft WHERE delivery_plan_id={delivery_plan_id}'
        sql3 = f'UPDATE tms.tms_delivery_plan_draft SET `status` = 40 WHERE `status` = 30 and delivery_plan_id={delivery_plan_id} order by in_dtm ASC limit 1'
        results1 = self.cd.query_data(sql1)
        results2 = self.cd.query_data(sql2)
        log.info(f"results1:{results1},results2:{results2}")
        if results1[0][0] == 'C' and '40' not in str(results2):
            self.cd.update_data(sql3)
            log.error(f"派车计划:{delivery_plan_id}基础数据异常,修复中...")

    def get_delivery_plan_id_by_draft(self, draft_id):
        """
        获取计划ID
        @param draft_id: 草稿ID
        @return:
        """
        sql = f"""SELECT delivery_plan_id FROM tms.tms_delivery_plan_draft WHERE draft_id = {draft_id}"""
        results = self.cd.query_data(sql)[0][0]
        if results:
            log.info(f"获取草稿:{draft_id}的计划ID为{results}")
            return results
        else:
            log.info(f"获取草稿:{draft_id}的计划ID失败")
            return False

    def get_dispatch_task_info(self, dispatch_id, info='*', task_type="stop"):
        """
        获取task列表信息
        @param dispatch_id: 派车单ID
        @param info: task 信息
        @param task_type: task类型 inventory,stop
        @return:
        """
        sql = f'''
            select
                {info}
            FROM
                tms.tms_dispatch_task tdt
            join tms.tms_address ta on
                tdt.address_id = ta.rec_id
            where
                tdt.dispatch_id = {dispatch_id}
                and task_type = '{task_type}'
            ORDER BY
                task_seq'''
        results = self.cd.query_data(sql)
        log.info(f"获取{dispatch_id}下task列表信息为{results}")
        return results

    def get_dispatch_info_by_order_id(self, order_id):
        """
        使用Order_id查询对应派车单ID及派车类型
        @param order_id: 订单ID
        @return:
        """
        sql = f"""SELECT vd.id,vd.delivery_type, vd.delivery_plan_id ,vd.group_point_id, tdt.task_seq 
                FROM tms.vm_dispatch vd 
                join tms.tms_dispatch_task tdt on tdt.dispatch_id = vd.id 
                join tms.vm_delivery_plan_point vdpp on vdpp.delivery_plan_id = vd.delivery_plan_id and vdpp.group_point_id =vd.group_point_id 
                join weee_comm.gb_order wcgo on vdpp.id = wcgo.tracking_number 
                WHERE wcgo.id ={order_id} and vdpp.seq = tdt.task_seq AND tdt.task_type ="stop" """
        res = self.cd.query_data(sql)
        if res:
            results = res[0]
            log.info(f"订单ID:{order_id}下的派车单信息为{results}")
            return results
        else:
            return False

    def get_delivery_type_by_dispatch_id(self, dispatch_id):
        """
        获取派车单类型
        :param dispatch_id:
        :return:
        """
        sql = f"""SELECT vd.delivery_type  FROM tms.vm_dispatch vd WHERE vd.id ={dispatch_id}"""
        results = self.cd.query_data(sql)[0][0]
        log.info(f"派车单:{dispatch_id}的类型为:{results}")
        return results

    def get_delivery_plan_id_by_dispatch_id(self, dispatch_id):
        """
        获取派车计划
        :param dispatch_id:
        :return:
        """
        sql = f"""SELECT delivery_plan_id  FROM tms.vm_dispatch vd WHERE vd.id ={dispatch_id}"""
        results = self.cd.query_data(sql)[0][0]
        log.info(f"派车单:{dispatch_id}的排车计划ID为:{results}")
        return results

    def get_group_point_id_by_dispatch_id(self, dispatch_id):
        """
        获取路线号ID
        :param dispatch_id:
        :return:
        """
        sql = f"""SELECT group_point_id  FROM tms.vm_dispatch vd WHERE vd.id ={dispatch_id}"""
        results = self.cd.query_data(sql)[0][0]
        log.info(f"派车单:{dispatch_id}的路线ID为:{results}")
        return results

    def del_driver_check_in_record(self, driver_user_id):
        """
        删除check in记录
        :param driver_user_id:
        :return:
        """
        sql = f"""DELETE FROM tms.dm_driver_check_in_record WHERE driver_user_id={driver_user_id}"""
        self.cd.update_data(sql)
        log.info(f"删除司机:{driver_user_id}的check in记录成功")

    def del_driver_time_clock_record(self, driver_user_id):
        """
        删除打卡记录
        :param driver_user_id:
        :return:
        """
        sql = f"""DELETE FROM tms.dms_driver_time_card_action_log WHERE driver_user_id={driver_user_id}"""
        self.cd.update_data(sql)
        log.info(f"删除司机:{driver_user_id}的打卡记录成功")

    def delete_risk_address(self, address):
        """
        删除风险地址记录
        @param address: 地址文本
        @return:
        """
        sql = f"""UPDATE tms.tms_address SET fixed ='N', risk ='N' WHERE user_address = '{address}'"""
        self.cd.update_data(sql)
        sql1 = f"""DELETE FROM `tms`.tms_address_risk WHERE address = '{address}'"""
        self.cd.update_data(sql1)
        log.info(f"删除风险地址{address}记录")

    def get_risk_address(self, address, info='*'):
        """
        获取风险地址记录
        @param address: 风险地址文本
        @param info: 信息
        @return:
        """
        sql = f"""SELECT {info} FROM `tms`.tms_address_risk WHERE address = '{address}'"""
        result = self.cd.query_data(sql)
        log.info(f"获取风险地址{address}记录")
        return result

    def get_address(self, address, info='*'):
        """
        获取地址记录
        @param address: 地址文本
        @param info: 信息
        @return:
        """
        sql = f"""SELECT {info} FROM `tms`.tms_address WHERE user_address = '{address}'"""
        result = self.cd.query_data(sql)
        log.info(f"获取地址{address}记录")
        return result

    def get_sub_region_name(self, sub_region_id):
        """
        根据sub_region_id获取sub_region_name
        :param sub_region_id:
        :return:
        """
        sql = f'SELECT sub_region_name FROM tms.tms_delivery_sub_region WHERE id ={sub_region_id};'
        results = self.cd.query_data(sql)
        log.info(f'SubRegionId: {sub_region_id} 的RegionName为: {results[0][0]}')
        return results[0][0]

    def get_sub_region_id_by_delivery(self, delivery_id):
        """
        根据delivery_id获取sub_region_id
        :param delivery_id:
        :return:
        """
        sql = f'SELECT sub_region_id FROM tms.tms_delivery_plan_sub_region_mapping sm join tms.vm_delivery_plan_mapping dm on dm.delivery_plan_id = sm.delivery_plan_id WHERE dm.delivery_id = {delivery_id} AND dm.status = "A";'
        result = self.cd.query_data(sql)
        log.info(f'SubRegionId: {result}')
        return [row[0] for row in result]

    def get_flex_config(self, sub_region_id):
        """
        根据sub_region_id获取Flex开关配置
        :param sub_region_id:
        :return:
        """
        sql = f'SELECT t.support_ic_compliance  FROM tms.tms_delivery_sub_region_config t WHERE t.sub_region_id ={sub_region_id}'
        results = self.cd.query_data(sql)
        log.info(f'SubRegionId: {sub_region_id} 的IC Compliance开关状态为: {results[0][0]}')
        return results[0][0]

    def get_tms_algorithm_config(self, sub_region_id, config_key='Flex cap'):
        """
        根据sub_region_id获取配置信息
        :param sub_region_id:
        :param config_key: 'Flex cap', 'Flex Mini van cap'
        :return:
        """
        sql = f"SELECT config_value FROM tms.tms_algorithm_config WHERE sub_region_id = {sub_region_id} and config_key = '{config_key}'"
        results = self.cd.query_data(sql)
        log.info(f'查询的SubRegionId:{sub_region_id}的{config_key}值为:{results[0][0]}')
        return results[0][0]

    def update_flex_cap(self, suv_cap, minivan_cap, sub_region_id):
        """
        更新Flex司机运力
        :param suv_cap:
        :param minivan_cap:
        :param sub_region_id:
        :return:
        """
        sql1 = f"UPDATE tms.tms_algorithm_config SET config_value = {suv_cap} WHERE config_key ='Flex cap' AND sub_region_id ={sub_region_id}"
        self.cd.update_data(sql1)
        log.info(f'更新sub_region_id:{sub_region_id}的 Flex cap 值为:{suv_cap}')
        sql2 = f"UPDATE tms.tms_algorithm_config SET config_value ={minivan_cap} WHERE config_key ='Flex Mini van cap' AND sub_region_id ={sub_region_id}"
        self.cd.update_data(sql2)
        log.info(f'更新sub_region_id:{sub_region_id}的 Flex Mini van cap 值为:{minivan_cap}')

    def update_delivery_zone(self, zone_value, sub_region_id):
        """
        更新地图上各类型司机的配送区域范围
        :param zone_value:  "0,0,0"
        :param sub_region_id:
        :return:
        """
        sql = f"UPDATE tms.tms_delivery_sub_region_config SET delivery_zone_radius ='{zone_value}' WHERE sub_region_id ={sub_region_id}"
        self.cd.update_data(sql)
        log.info(f'更新sub_region_id{sub_region_id}地图上各类型司机的配送区域范围为{zone_value}')

    def update_flex_config(self, release_time, pickup_time, sub_region_id, rescue_time):
        """
        新Flex配置时间
        :param release_time: 路线释放时间
        :param pickup_time: 路线去仓库取货时间
        :param sub_region_id: 区域ID
        :param rescue_time: 救援时间
        :return:
        """
        sql = f"""UPDATE
                    tms.tms_delivery_sub_region_config
                SET route_release_time = '{release_time}',
                    route_release_date = 'DELIVERY_DAY',
                    pickup_time= '{pickup_time}',
                    ic_compliance_rescue_time = '{rescue_time}'
                WHERE
                sub_region_id = {sub_region_id}"""
        self.cd.update_data(sql)
        log.info(f'区域{sub_region_id}Flex的release_time:{release_time}, pickup_time: {pickup_time}')

    def get_route_info(self, draft_id):
        """
        获取路线信息
        :param draft_id:
        :return:
        """
        sql = f'SELECT rec_id ,route_id  FROM tms.tms_delivery_plan_draft_route WHERE draft_id = {draft_id} order by point_count DESC'
        results = self.cd.query_data(sql)
        log.info(f'draft_id:{draft_id}路线信息为{results}')
        return results

    def get_route_type(self, draft_id, route_id):
        """
        获取路线类型
        :param draft_id:
        :param route_id:
        :return:
        """
        sql = f'SELECT route_type FROM tms.tms_delivery_plan_draft_route WHERE draft_id = {draft_id} AND route_id ={route_id}'
        results = self.cd.query_data(sql)
        log.info(f'draft_id:{draft_id}路线号为{route_id}的路线类型为{results[0][0]}')
        return results[0][0]

    def get_sub_region_id(self, sub_region_name):
        """
        根据sub_region_name获取sub_region_id
        :param sub_region_name:
        :return:
        """
        sql = f'SELECT sub_region_name FROM tms.tms_delivery_sub_region WHERE id ={sub_region_name};'
        results = self.cd.query_data(sql)
        log.info(f'获取sub_region_name{sub_region_name}的RegionID为{results[0][0]}')
        return results[0][0]

    def get_sub_region_id_by_delivery_plan(self, delivery_plan_id):
        """
        根据sub_region_name获取sub_region_id
        :param delivery_plan_id:
        :return:
        """
        sql = f'SELECT sub_region_id FROM tms.vm_delivery_plan_point WHERE delivery_plan_id ={delivery_plan_id}'
        results = self.cd.query_data(sql)
        log.info(f'获取delivery_plan{delivery_plan_id}的RegionID为{results[0][0]}')
        return results[0][0]

    def get_sub_region_ids(self, region_id):
        """
        根据region_id获取sub_region_ids
        :param region_id:
        :return:
        """
        sql = f'SELECT id FROM tms.tms_delivery_sub_region WHERE delivery_region_id ={region_id};'
        results = self.cd.query_data(sql)
        sub_region_ids = [x[0] for x in results]
        log.info(f'region_id:{region_id}下的sub_region_ids为{sub_region_ids}')
        return sub_region_ids

    def get_schedule_config(self, driver_user_id):
        """
        获取司机schedule配置信息
        :param driver_user_id:
        :return:
        """
        sql = f'SELECT week_day FROM tms.tms_w2_schedule_config WHERE driver_user_id = {driver_user_id} AND status ="A"'
        res = self.cd.query_data(sql)
        if res:
            results = [x[0] for x in res]
            log.info(f'获取司机 {driver_user_id} 的Schedule配置为:{results}')
            return results
        else:
            return False

    def get_signup_id(self, driver_user_id, delivery_date):
        """
        获取signup ID
        :param driver_user_id:
        :param delivery_date:
        :return:
        """
        sql = f'SELECT id FROM tms.vm_driver_signup WHERE delivery_date="{delivery_date}" AND driver_user_id={driver_user_id} AND status !="X"'
        res = self.cd.query_data(sql)
        if res:
            results = res[0]
            log.info(f'获取司机 {driver_user_id} 在 {delivery_date} 下的SignUpID为:{results[0]}')
            return results[0]
        else:
            log.info(f'未获取到司机 {driver_user_id} 在 {delivery_date} 下的SignUpID')
            return False

    def get_adjustment_fee_info(self, fee_id=None, driver_user_id=None, delivery_date=None, reason_code=None,
                                status='pending', info='*'):
        """
        获取adjustment费用ID
        :param fee_id:
        :param driver_user_id:
        :param reason_code:
        :param delivery_date:
        :param status: pending，approved，rejected
        :param info:
        :return:
        """
        if fee_id:
            sql = f'''SELECT {info} FROM tms.tms_driver_fee_adjustment WHERE id = {fee_id}'''
        elif reason_code:
            sql = f'''SELECT {info} FROM tms.tms_driver_fee_adjustment WHERE status = "{status}" AND driver_user_id = {driver_user_id} AND reason_code ="{reason_code}" ORDER BY id DESC'''
        else:
            sql = f'''SELECT {info} FROM tms.tms_driver_fee_adjustment WHERE status = "{status}" AND driver_user_id = {driver_user_id} AND delivery_date ="{delivery_date}" ORDER BY id DESC'''
        res = self.cd.query_data(sql)
        if res:
            if info != '*' and ',' not in info:
                results = res[0]
                log.info(f'获取adjustment费用信息为:{results[0]}')
                return results[0]
            else:
                log.info(f'获取adjustment费用信息为:{res[0]}')
                return res[0]
        else:
            log.info(f'未获取到adjustment费用信息')
            return False

    def get_signup_info(self, signup_id):
        """
        获取signup相关信息
        :param signup_id:
        :return:
        """
        sql = f'SELECT group_point_id,status,assigned_address_total FROM tms.vm_driver_signup x WHERE id = {signup_id}'
        results = self.cd.query_data(sql)
        log.info(f'获取signup相关信息为{results[0]}')
        return results[0]

    def get_vehicle_info(self, vehicle_id):
        """
        获取车辆信息
        :param vehicle_id:
        :return:
        """
        sql = f"""SELECT
                id,
                delivery_station_id,
                current_mileage,
                acquisition,
                license_plate,
                vehicle_number,
                vehicle_type_id,
                vehicle_vin,
                vehicle_status,
                start_date,
                end_date,
                registration_end_date,
                last_trans_fluid_change_date,
                last_tire_replacement_date ,
                last_oil_change_date ,
                last_brake_replacement_date ,
                last_alignment_date
            FROM
                tms.vm_vehicle
            WHERE
                id = {vehicle_id}"""
        results = self.cd.query_data(sql)
        log.info(f'获取车辆ID:{vehicle_id}的相关信息为{results[0]}')
        return results[0]

    def get_vehicle_type_info(self, vehicle_id):
        """
        获取车辆类型信息
        :param vehicle_id:
        :return:
        """
        sql = f"""SELECT
                id,
                vehicle_year,
                vehicle_make,
                vehicle_model,
                vehicle_capacity,
                payload,
                mpg_city,
                mpg_highway
            FROM
                tms.vm_vehicle_type
            WHERE
                id = {vehicle_id}"""
        results = self.cd.query_data(sql)
        log.info(f'获取车辆类型ID:{vehicle_id}的相关信息为{results[0]}')
        return results[0]

    def del_vehicle_type(self, vehicle_id: int = None, vehicle_make: str = None):
        """
        删除车辆类型信息
        :param vehicle_id:
        :param vehicle_make:
        :return:
        """
        if vehicle_id:
            sql = f'''DELETE FROM tms.vm_vehicle_type WHERE id = {vehicle_id}'''
            log.info(f'删除ID为{vehicle_id}的车辆类型信息')
        elif vehicle_make:
            sql = f'''DELETE FROM tms.vm_vehicle_type WHERE vehicle_make = "{vehicle_make}"'''
            log.info(f'删除vehicle_make为{vehicle_make}的车辆类型信息')
        else:
            raise Exception('未传入车辆类型ID或车辆类型')
        self.cd.update_data(sql)

    def del_vehicle(self, vehicle_id: int):
        """
        删除车辆信息
        :param vehicle_id:
        :return:
        """
        sql = f'DELETE FROM tms.vm_vehicle WHERE id = {vehicle_id}'
        self.cd.update_data(sql)
        log.info(f'删除ID为{vehicle_id}的车辆信息')

    def update_signup(self, signup_id: int):
        """
        将signup记录更新为未审核状态
        :param signup_id:
        :return:
        """
        sql = f'UPDATE tms.vm_driver_signup SET group_point_id = NULL ,status ="A", assigned_address_total =NULL WHERE id={signup_id}'
        self.cd.update_data(sql)
        log.info(f'将signup记录ID为{signup_id}更新为未审核状态')

    def update_draft_split_flag(self, draft_id: int, split_flag: int):
        """
        更新排车草稿是否已拆分路线
        :param draft_id:
        :param split_flag:
        :return:
        """
        sql = f'UPDATE tms.tms_delivery_plan_draft SET split_flag = {split_flag} WHERE draft_id = {draft_id}'
        self.cd.update_data(sql)
        log.info(f'更新排车草稿:{draft_id}拆分路线状态为:{split_flag}')

    def update_adjust_fee_creator_id(self, fee_id, creator_id=10923090):
        """
        更新Adjustment Fee创建人
        :param fee_id:
        :param creator_id:
        :return:
        """
        sql = f'UPDATE tms.tms_driver_fee_adjustment SET creator_id = {creator_id} WHERE id = {fee_id}'
        self.cd.update_data(sql)
        log.info(f'更新Adjustment Fee ID为{fee_id}的创建人更新为{creator_id}')

    def delete_signup(self, driver_user_id, delivery_date=None):
        """
        取消signup记录
        :param driver_user_id:
        :param delivery_date:
        :return:
        """
        if delivery_date:
            sql = f'UPDATE tms.vm_driver_signup set status = "X" WHERE delivery_date="{delivery_date}" AND driver_user_id={driver_user_id}'
        else:
            sql = f'UPDATE tms.vm_driver_signup set status = "X" WHERE driver_user_id={driver_user_id}'
        self.cd.update_data(sql)
        log.info(f'取消司机{driver_user_id}的SignUp记录')

    def update_signup_rec_update_id(self, driver_user_id, sub_region_id=None, delivery_date=None):
        """
        修改signup操作人为系统操作
        :param driver_user_id:
        :param sub_region_id:
        :param delivery_date:
        :return:
        """

        if delivery_date:
            sql = f'UPDATE tms.vm_driver_signup SET assigned_address_total = 40 WHERE driver_user_id = {driver_user_id} AND delivery_date="{delivery_date}"'
        elif sub_region_id:
            sql = f'UPDATE tms.vm_driver_signup SET rec_update_id = 111111, assigned_address_total = NULL, sub_region_id={sub_region_id} WHERE driver_user_id = {driver_user_id} AND (rec_update_id != 111111 OR assigned_address_total is NOT NULL OR sub_region_id !={sub_region_id})'
        else:
            sql = f'UPDATE tms.vm_driver_signup SET rec_update_id = 111111, assigned_address_total = NULL WHERE driver_user_id = {driver_user_id} AND (rec_update_id != 111111 OR assigned_address_total is NOT NULL OR sub_region_id !={sub_region_id})'
        self.cd.update_data(sql)
        log.info(f'修改司机:{driver_user_id}的signup记录,sql为:{sql}')

    def delete_schedule_capacity(self, driver_user_id):
        """
        删除司机的schedule记录
        :param driver_user_id:
        :return:
        """
        sql = f'delete FROM tms.dm_schedule_capacity WHERE driver_user_id = {driver_user_id}'
        self.cd.update_data(sql)
        log.info(f'删除司机{driver_user_id}的schedule记录')

    def get_dispatch_data(self, dispatch_id):
        """
        获取派车单delivery_plan_id,group_point_id信息
        :param dispatch_id:
        :return:
        """
        sql = f"""SELECT delivery_plan_id,group_point_id FROM tms.vm_dispatch WHERE id ={dispatch_id}"""
        results = self.cd.query_data(sql)
        log.info("*" * 50 + f"\033[1;35m 派车单ID：{dispatch_id} \033[0m" + "*" * 50)
        log.info(f'获取派车单{dispatch_id}的delivery_plan_id,group_point_id信息为{results[0]}')
        return results[0]

    def get_out_layer_rate(self, sub_region_id, version, flex_flag=0):
        """
        获取最外围运费系数
        :param sub_region_id:
        :param version:
        :param flex_flag:
        :return:
        """
        if flex_flag:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'outLayerRate' AND version =
            {version} AND sub_region_id ={sub_region_id} and `type` =4"""
        else:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'outLayerRate' AND version =
            {version} AND sub_region_id ={sub_region_id} and `type` in (0,1)"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'outLayerRate' AND version =
                    {version} AND sub_region_id ={sub_region_id} and `type` =0"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, flex_flag为:{flex_flag}的最外围运费系数outLayerRate:为 {results[0][0]}')
            return float(results[0][0])
        else:
            results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'获取SubRegion:{sub_region_id}, flex_flag为:{flex_flag}的最外围运费系数outLayerRate:为 {results[0][0]}')
                return float(results[0][0])
            else:
                log.info(f'获取SubRegion:{sub_region_id}, flex_flag为:{flex_flag}的最外围运费系数outLayerRate:为 1')
                return 1

    def get_distance_rate(self, sub_region_id, version):
        """
        获取距离系数
        :param sub_region_id:
        :param version:
        :return:
        """
        sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'distanceFee' AND version =
            {version} AND sub_region_id ={sub_region_id}"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'distanceFee' AND version =
                    {version} AND sub_region_id =999"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, version为:{version}的距离系数distanceFeeRate:为 {results[0][0]}')
            return results[0][0]
        else:
            results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'获取SubRegion:{sub_region_id}, version为:{version}的系数未找到, 取 999 默认subRegion距离系数distanceFeeRate:为 {results[0][0]}')
                return results[0][0]
            else:
                log.info(f'未获取到SubRegion:{sub_region_id}, version为:{version}的距离系数')
                return False

    def get_address_count_rate(self, sub_region_id, version):
        """
        获取站点系数
        :param sub_region_id:
        :param version:
        :return:
        """
        sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'addressCountRate' AND version =
            {version} AND sub_region_id ={sub_region_id}"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'addressCountRate' AND version =
                    {version} AND sub_region_id =999"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, version为:{version}的距离系数distanceFeeRate:为 {results[0][0]}')
            return results[0][0]
        else:
            results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'获取SubRegion:{sub_region_id}, version为:{version}的系数未找到, 取 999 默认subRegion距离系数distanceFeeRate:为 {results[0][0]}')
                return results[0][0]
            else:
                log.info(f'未获取到SubRegion:{sub_region_id}, version为:{version}的站点系数')
                return False

    def get_prop_22_fee(self, sub_region_id, version):
        """
        获取22法令公式系数
        :param sub_region_id:
        :param version:
        :return:
        """
        sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'prop22Fee' AND version =
            {version} AND sub_region_id ={sub_region_id}"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'prop22Fee' AND version =
                    {version} AND sub_region_id =999"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, version为:{version}的22法令公式系数prop22Fee:为 {results[0][0]}')
            return results[0][0]
        else:
            results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'SubRegion:{sub_region_id}, version为:{version}的系数未找到, 取 999 默认subRegion的22法令公式系数prop22Fee:为 {results[0][0]}')
                return results[0][0]
            else:
                log.info(f'未获取到SubRegion:{sub_region_id}, version为:{version}的22法令公式系数')
                return False

    def get_apartment_rate(self, sub_region_id, version):
        """
        获取APT系数
        :param sub_region_id:
        :param version:
        :return:
        """
        sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'apartmentFee' AND version =
            {version} AND sub_region_id ={sub_region_id}"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'apartmentFee' AND version =
                    {version} AND sub_region_id =999"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, version为:{version}的APT系数apartmentFee:为 {results[0][0]}')
            return results[0][0]
        else:
            results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'获取SubRegion:{sub_region_id}, version为:{version}的系数未找到, 取 999 默认subRegion的APT系数apartmentFee:为 {results[0][0]}')
                return results[0][0]
            else:
                log.info(f'未获取到SubRegion:{sub_region_id}, version为:{version}的APT系数')
                return False

    def get_special_distance_rate(self, sub_region_id, version, route_type, company_id=None):
        """
        获取特殊距离系数
        :param sub_region_id:
        :param version:
        :param route_type: 线路类型 0-all 1-normal 2-hot 3-alcohol 4-flex 5-三方司机与company_id对应 6-flex-suv
        :param company_id: 司机所在公司ID
        :return:
        """
        if company_id:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialDistanceRate' AND version =
            {version} AND sub_region_id ={sub_region_id} AND `type` = {route_type} and company_id={company_id}"""
        else:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialDistanceRate' AND version =
            {version} AND sub_region_id ={sub_region_id} AND `type` = {route_type}"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialDistanceRate' AND version =
            {version} AND sub_region_id ={sub_region_id} AND `type` = 0"""
        sql2 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialDistanceRate' AND version =
            {version} AND sub_region_id ={sub_region_id} AND `type` = 4"""

        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, version为:{version}的特殊距离系数specialDistanceRate:为 {results[0][0]}')
            return float(results[0][0])
        else:
            if route_type == 6:
                results = self.cd.query_data(sql2)
            else:
                results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'获取SubRegion:{sub_region_id}, version为:{version}的特殊距离系数specialDistanceRate:为 {results[0][0]}')
                return float(results[0][0])
            else:
                log.info(f'未获取到SubRegion:{sub_region_id}, version为:{version}的特殊距离系数')
                return False

    def get_special_point_rate(self, sub_region_id, version, route_type, company_id=None):
        """
        获取特殊派送点系数
        :param sub_region_id:
        :param version:
        :param route_type: 线路类型 0-all 1-normal 2-hot 3-alcohol 4-flex 5-三方司机与company_id对应 6-flex-suv
        :param company_id: 司机所在公司ID
        :return:
        """
        if company_id:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialPointRate' AND version =
            {version} AND sub_region_id ={sub_region_id} AND `type` = {route_type} and company_id={company_id}"""
        else:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialPointRate' AND version =
            {version} AND sub_region_id ={sub_region_id} AND `type` = {route_type}"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialPointRate' AND version =
            {version} AND sub_region_id ={sub_region_id} AND `type` = 0"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, version为:{version}的特殊派送点系数specialPointRate:为 {results[0][0]}')
            return float(results[0][0])
        else:
            results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'获取SubRegion:{sub_region_id}, version为:{version}的特殊派送点系数specialPointRate:为 {results[0][0]}')
                return float(results[0][0])
            else:
                log.info(f'未获取到SubRegion:{sub_region_id}, version为:{version}的特殊派送点系数')
                return False

    def get_special_zipcode(self, version):
        """
        获取特殊zipcode数据
        :param sub_region_id:
        :param version:
        :return:
        """
        sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'specialZipcodeFee' AND version =
            {version} AND sub_region_id =999"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取version为:{version}的特殊zipcode数据:为 {results[0][0]}')
            return results[0][0]
        else:
            log.info(f'未获取到version为:{version}的特殊zipcode数据')
            return False

    def get_mini_point_num(self, sub_region_id, version, flex_flag=0):
        """
        获取该地区运费最小送货点数量
        :param sub_region_id:
        :param version:
        :param flex_flag:
        :return:
        """
        if flex_flag:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'minPointNumber' AND version =
            {version} AND sub_region_id ={sub_region_id} and `type` =4"""
        else:
            sql = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'minPointNumber' AND version =
            {version} AND sub_region_id ={sub_region_id} and `type` in (0,1)"""
        sql1 = f"""SELECT expression FROM tms.tms_driver_fee_config WHERE config_name = 'minPointNumber' AND version =
                    {version} AND sub_region_id ={sub_region_id} and `type` =0"""
        results = self.cd.query_data(sql)
        if results:
            log.info(
                f'获取SubRegion:{sub_region_id}, flex_flag为:{flex_flag}的最小送货点数量:为 {results[0][0]}')
            return int(results[0][0])
        else:
            results = self.cd.query_data(sql1)
            if results:
                log.info(
                    f'获取SubRegion:{sub_region_id}, flex_flag为:{flex_flag}的最小送货点数量:为 {results[0][0]}')
                return int(results[0][0])
            else:
                log.info(f'获取SubRegion:{sub_region_id}, flex_flag为:{flex_flag}的最小送货点数量:为 15')
                return 15

    def get_delivery_plan_info(self, delivery_plan_id, info='*'):
        """
        获取派车计划信息
        :param delivery_plan_id:
        :param info:
        :return:
        """
        sql = f"""SELECT {info} FROM tms.vm_delivery_plan WHERE id = {delivery_plan_id} and status != 'X'"""
        results = self.cd.query_data(sql)
        text = results[0][0].strftime('%Y-%m-%d %H:%M:%S')
        log.info(f'派车计划{delivery_plan_id}的{info}信息为: {text}')
        return results[0]

    def reset_delivery_plan(self, delivery_plan_id):
        """
        将delivery应用状态改为未应用
        :param delivery_plan_id:
        :return:
        """
        sql1 = f"UPDATE tms.vm_dispatch SET status ='X' WHERE delivery_plan_id ={delivery_plan_id} AND status ='A'"
        self.cd.update_data(sql1)
        log.info(f"删除delivery_plan_id:{delivery_plan_id}已应用的派车单")
        sql2 = f"UPDATE tms.vm_delivery_plan SET status ='A' WHERE id = {delivery_plan_id}"
        self.cd.update_data(sql2)
        log.info(f"更新delivery_plan_id:{delivery_plan_id}应用状态为未应用")
        sql3 = f"UPDATE tms.tms_delivery_plan_draft SET status =30 WHERE delivery_plan_id ={delivery_plan_id} AND status =40"
        self.cd.update_data(sql3)
        log.info(f"更新delivery_plan_id:{delivery_plan_id}已应用Draft状态为未应用")
        sql4 = f'DELETE FROM tms.dp_dispatch_replan_point_record WHERE point_id in (SELECT id from tms.vm_delivery_plan_point WHERE delivery_plan_id = {delivery_plan_id});'
        self.cd.update_data(sql4)
        log.info(f"删除delivery_plan_id:{delivery_plan_id}的Re plan数据")
        sql5 = f'DELETE FROM tms.tms_dispatch_task where task_refer_id in (SELECT id FROM  tms.vm_delivery_plan_point WHERE delivery_plan_id = {delivery_plan_id})'
        self.cd.update_data(sql5)
        log.info(f"删除delivery_plan_id:{delivery_plan_id}的task数据")

    def get_delivery(self, region_id, num=10):
        """
        获取派车计划信息
        :param region_id:
        :param num:
        :return:
        """
        sql = f"""SELECT x.id, x.status, x.delivery_type, x.delivery_date, x.note 
                    FROM weee_comm.gb_delivery x 
                    WHERE 
                    sales_org_id in (
                    SELECT x.sales_org_id FROM tms.tms_delivery_sub_region x WHERE x.delivery_region_id={region_id})
                    AND status ='A'
                    AND order_packing_num_done is NULL
                    ORDER BY x.id DESC limit {num};"""
        results = self.cd.query_data(sql)
        log.info(f'获取RegionID:{region_id}下最近的{num}个Delivery ID信息为{results}')
        return results

    def get_delivery_id(self, delivery_plan_id):
        """
        获取发货批次delivery_id
        :param delivery_plan_id: 发货计划
        :return:
        """
        sql = f"""SELECT x.delivery_ids  FROM tms.vm_delivery_plan x WHERE id={delivery_plan_id}"""
        results = self.cd.query_data(sql)
        log.info(f'获取delivery_plan_id:{delivery_plan_id}的发货批次为{results[0][0]}')
        return results[0][0]

    def get_delivery_date(self, delivery_id):
        """
        获取发货批次配送日期
        :param delivery_id: 发货批次ID
        :return:
        """
        sql = f"""SELECT delivery_date FROM  weee_comm.gb_invoice WHERE delivery_id = {delivery_id} LIMIT 1 ;"""
        results = self.cd.query_data(sql)
        log.info(f'获取发货批次:{delivery_id}的配送日期为{results[0][0]}')
        return str(results[0][0])

    def get_package_info(self, dispatch_id):
        """
        获取所有包裹信息
        :param dispatch_id:
        :return:
        """
        sql = f"""
        SELECT
        dpp.seq,
        tp.tracking_num AS tp_tracking_num,
        tp. STATUS AS tp_status,
        tp.storage_type AS tp_storage_type
        FROM tms.vm_delivery_plan_point dpp
        INNER JOIN tms.vm_dispatch d ON dpp.delivery_plan_id = d.delivery_plan_id AND dpp.group_point_id = d.group_point_id 
        LEFT JOIN tms.tms_package tp ON dpp.id = tp.stop_id
        WHERE d.id = {dispatch_id} ORDER BY dpp.seq ASC;
        """
        results = self.cd.query_data(sql)
        log.info(
            f'获取派车单{dispatch_id}下所有包裹信息成功')
        return results

    def get_package_by_type(self, dispatch_id, package_type):
        """
        获取指定类型包裹信息
        :param dispatch_id:
        :param package_type: F,Z,D
        :return:
        """
        sql = f"""
        SELECT
        dpp.seq,
        tp.tracking_num AS tp_tracking_num,
        tp. STATUS AS tp_status,
        tp.storage_type AS tp_storage_type
        FROM tms.vm_delivery_plan_point dpp
        INNER JOIN tms.vm_dispatch d ON dpp.delivery_plan_id = d.delivery_plan_id AND dpp.group_point_id = d.group_point_id 
        LEFT JOIN tms.tms_package tp ON dpp.id = tp.stop_id
        WHERE d.id = {dispatch_id} and tp.storage_type='{package_type}' ORDER BY dpp.seq ASC;
        """
        results = self.cd.query_data(sql)
        log.info(f'获取派车单:{dispatch_id}下包裹类型为{package_type}的包裹信息为{results}')
        return results

    def get_package_types(self, dispatch_id):
        """
        获取派车单下包裹类型枚举
        :param dispatch_id:
        :return:
        """
        sql = f"""
        SELECT DISTINCT tp.storage_type FROM tms.vm_delivery_plan_point dpp
        INNER JOIN tms.vm_dispatch d ON dpp.delivery_plan_id = d.delivery_plan_id AND dpp.group_point_id = d.group_point_id 
        LEFT JOIN tms.tms_package tp ON dpp.id = tp.stop_id
        WHERE d.id = {dispatch_id} ORDER BY dpp.seq ASC;"""
        results = self.cd.query_data(sql)
        log.info(f'获取{dispatch_id}下包裹类型枚举为{results}')
        return results

    def change_package_status(self, dispatch_id, statues="packed"):
        """
        修改派车单包裹状态
        @param dispatch_id: 派车单ID
        @param statues: 包裹状态
        @return:
        """
        sql = f"""UPDATE tms.tms_package p 
                INNER JOIN tms.vm_delivery_plan_point dpp ON p.stop_id = dpp.id
                INNER JOIN tms.vm_dispatch d ON dpp.delivery_plan_id = d.delivery_plan_id
                SET p.status = '{statues}'
                WHERE d.id = {dispatch_id}
                AND dpp.group_point_id = (SELECT group_point_id FROM tms.vm_dispatch WHERE id = {dispatch_id})"""
        self.cd.update_data(sql)
        log.info(f'更新{dispatch_id}包裹状态为{statues}')

    def del_delivered_photo(self, delivery_plan_id, group_point_id):
        """
        删除路线图片
        :param delivery_plan_id:
        :param group_point_id:
        :return:
        """
        sql = f"""UPDATE tms.vm_delivery_plan_point p 
        SET p.delivered_photo = NULL,
            p.delivered_photo_uploading_time = NULL
        WHERE
            delivery_plan_id = {delivery_plan_id}
            AND p.group_point_id = {group_point_id}"""
        self.cd.update_data(sql)
        log.info(f'删除delivery_plan_id:{delivery_plan_id},group_point_id:{group_point_id}配送图片成功')

    # *********司机运费相关查询*********

    def get_delivery_num(self, delivery_plan_id=6405):
        """
        获取路线号
        :param delivery_plan_id: 计划ID
        :return:
        """
        sql = f"""select group_point_id FROM  vm_dispatch  where
        delivery_plan_id = {delivery_plan_id} AND status = 'A' AND dispatch_type !='G'"""
        result = self.cd.query_data(sql)
        group_point_ids = [item[0] for item in result]
        log.info(f"该派车计划{delivery_plan_id}下所有路线号为：{group_point_ids}")
        return group_point_ids

    def dispatch_id(self, plan_id, group_point_id):
        """
        获取派车单ID
        """
        sql = f"""select id FROM  tms.vm_dispatch p where p.delivery_plan_id ={plan_id} AND p.group_point_id ={group_point_id} AND p.status ='A'"""
        results = self.cd.query_data(sql)
        return results[0][0]

    def get_dispatch_fee(self, dispatch_id):
        """
        获取dispatch fee
        @param dispatch_id:
        @return:
        """
        sql = f"""select dispatch_fee,dispatch_tip FROM  tms.vm_dispatch  where id = {dispatch_id}"""
        results = self.cd.query_data(sql)
        log.info(f"dispatch_fee：{results[0]}")
        return round(results[0][0] + results[0][1], 2)

    def get_report_fee(self, dispatch_id):
        """
        获取dispatch fee
        @param dispatch_id:
        @return:
        """
        sql = f"""SELECT x.sub_total_fee ,x.tip_fee FROM tms.tms_driver_fee_report x WHERE dispatch_id = {dispatch_id}"""
        results = self.cd.query_data(sql)
        log.info(f"report_fee：{results[0]}")
        return round(results[0][0] + results[0][1], 2)

    def get_delivery_info(self, delivery_plan_id=6405, group_point_id=1):
        """
        获取路线信息
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"""
        SELECT 
        vm_dispatch.group_point_id, vm_dispatch.flex_flag, vm_dispatch.driver_user_id, vm_driver.name,
        vm_dispatch.distance, vm_dispatch.dispatch_tip, count(*) AS point_count, sum(vm_delivery_plan_point.driver_fee_B) 
        AS zipcode_fee, sum(if(vm_delivery_plan_point.address_type='H',0,1)) AS 'Original APT', count(DISTINCT IF(
        vm_delivery_plan_point.address_type='H',null , concat(ta.latitude,ta.longitude
        ))) AS APT, vm_dispatch.sub_region_id, vm_dispatch.dispatch_type, vm_driver.car_type 
        FROM 
        vm_dispatch JOIN vm_delivery_plan_point ON vm_delivery_plan_point.group_point_id =  vm_dispatch.group_point_id AND 
        vm_delivery_plan_point.delivery_plan_id= vm_dispatch.delivery_plan_id 
        LEFT JOIN 
        vm_driver ON vm_driver.driver_user_id= vm_dispatch.driver_user_id 
        LEFT JOIN tms.tms_address ta on
        vm_delivery_plan_point.address_id = ta.rec_id
        WHERE 
        vm_dispatch.delivery_plan_id = {delivery_plan_id} 
        AND `vm_delivery_plan_point`.`group_point_id` = {group_point_id} 
        AND vm_dispatch.`status`="A" 
        GROUP BY vm_dispatch.group_point_id;
        """
        result = self.cd.query_data(sql)[0]
        return result

    def get_dispatch_distance(self, delivery_plan_id=6405, group_point_id=1):
        """
        获取路线距离
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"""
        SELECT routing_response, summary FROM tms.vm_dispatch WHERE delivery_plan_id ={delivery_plan_id} AND group_point_id ={group_point_id}
        """
        result = self.cd.query_data(sql)[0]
        return result

    def get_point_count(self, delivery_plan_id, group_point_id):
        """
        获取实际配送站点数量
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"""
        SELECT COUNT(*)
            FROM tms.tms_dispatch_task AS dt
            JOIN tms.vm_dispatch AS d ON dt.dispatch_id = d.id
        WHERE d.delivery_plan_id = {delivery_plan_id}
            AND d.status = "A"
            AND d.group_point_id = {group_point_id}
            AND dt.task_status != 40
            AND dt.task_type = "stop";
        """
        result = self.cd.query_data(sql)[0][0]
        return result

    def get_cancel_task_info(self, delivery_plan_id, group_point_id):
        """
        获取取消的站点信息
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"""
        SELECT
        COUNT(CASE WHEN vm_delivery_plan_point.address_type = 'H' THEN NULL ELSE 1 END) AS 'apt_num',
        SUM(vm_delivery_plan_point.driver_fee_B) AS 'zipcode_fee'
        FROM
        vm_delivery_plan_point
        WHERE
        vm_delivery_plan_point.delivery_plan_id = {delivery_plan_id}
        AND vm_delivery_plan_point.group_point_id = {group_point_id}
        AND vm_delivery_plan_point.seq IN (
        SELECT dt.task_seq
        FROM tms.tms_dispatch_task AS dt
        JOIN tms.vm_dispatch AS d ON dt.dispatch_id = d.id
        WHERE d.delivery_plan_id = {delivery_plan_id}
        AND d.status = 'A'
        AND d.group_point_id = {group_point_id}
        AND dt.task_status = 40
        AND dt.task_type = 'stop')
        """
        result = self.cd.query_data(sql)[0]
        return result

    def get_engagement_time(self, delivery_plan_id=6405, group_point_id=1):
        """
        获取路线信息
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"""
        SELECT * FROM  vm_dispatch  where delivery_plan_id = {delivery_plan_id} 
        AND group_point_id={group_point_id} AND status = "A";
        """
        result = self.cd.query_data(sql)[0]
        dispatch_id = result[0]
        sql1 = f"""
        SELECT actual_start_dtm FROM tms_dispatch_task WHERE task_type = "stop" AND dispatch_id = {dispatch_id};
        """
        result1 = self.cd.query_data(sql1)
        sql2 = f"""
        SELECT actual_finish_dtm FROM tms_dispatch_task WHERE task_type = "stop" AND dispatch_id = {dispatch_id};
        """
        result2 = self.cd.query_data(sql2)
        result3 = []
        result4 = []
        if result1 and result2:
            for i in result1:
                if i[0] != 0:
                    result3.append(i[0])
            for i in result2:
                if i[0] != 0:
                    result4.append(i[0])
            if result3 and result4:
                minn_start = min(result3)
                max_start = max(result3)
                minn_end = min(result4)
                max_end = max(result4)
                if (max_end - minn_start) >= 28800:  # 最大工作时长大于8小时按8小时计算
                    engagement_time = 8
                elif max_end - max_start >= 10800:  # 最大结束时间比最大开始时间大3小时时,结束时间按最大开始时间计算
                    engagement_time = (max_start - minn_start) / 3600
                    log.info("超过最大8小时送货时间，工作时长按8小时计算 ")
                else:
                    engagement_time = (max_end - minn_start) / 3600
                log.info(
                    f"最小开始时间：{minn_start}, 最大开始时间：{max_start}, 最小完成时间{minn_end}, 最大完成时间：{max_end}")
                log.info(f"工作时长：{engagement_time:.2f}小时")
                return engagement_time
            else:
                log.info("配送时间数据不全，无法计算工作时长")
                return 0
        else:
            log.info("配送时间数据不全，无法计算工作时长")
            return 0

    def get_engagement_time_by_point(self, delivery_plan_id=6405, group_point_id=1):
        """
        获取配送时间
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"""
        SELECT vd.check_in_time FROM tms.vm_dispatch vd WHERE vd.delivery_plan_id ={delivery_plan_id} 
        AND vd.group_point_id ={group_point_id} AND vd.status ='A'"""
        result = self.cd.query_data(sql)
        if result[0][0]:
            check_in_time = result[0][0] - 8 * 3600  # point表时间为UTC时间,转为时间戳时相差8小时,此处减去8小时,数值与point表一致
        else:
            check_in_time = 0
        sql1 = f"""
        SELECT x.delivery_start_time  FROM tms.vm_delivery_plan_point x WHERE x.delivery_plan_id ={delivery_plan_id} 
        AND x.group_point_id ={group_point_id} ORDER BY x.delivery_start_time ASC"""
        result1 = self.cd.query_data(sql1)
        sql2 = f"""
        SELECT x.delivery_end_time  FROM tms.vm_delivery_plan_point x WHERE x.delivery_plan_id ={delivery_plan_id} 
        AND x.group_point_id ={group_point_id} ORDER BY x.delivery_end_time ASC"""
        result2 = self.cd.query_data(sql2)
        result3 = []
        result4 = []
        if result1 and result2:
            for i in result1:
                if i[0]:
                    date_time_obj = datetime.datetime.strptime(str(i[0]), '%Y-%m-%d %H:%M:%S')
                    timestamp = int(time.mktime(date_time_obj.timetuple()))
                    result3.append(timestamp)
            for j in result2:
                if j[0]:
                    date_time_obj = datetime.datetime.strptime(str(j[0]), '%Y-%m-%d %H:%M:%S')
                    timestamp = int(time.mktime(date_time_obj.timetuple()))
                    result4.append(timestamp)
            if result3 and result4:
                minn_start = min(result3)
                max_start = max(result3)
                minn_end = min(result4)
                max_end = max(result4)
                if check_in_time:
                    check_in_time_date = datetime.datetime.fromtimestamp(check_in_time).strftime("%Y-%m-%d %H:%M:%S")
                    log.info(f"check_in_time时间：{check_in_time}, check_in_time_date日期：{check_in_time_date}")
                    if (max_end - check_in_time) >= 28800:  # 最大工作时长大于8小时按8小时计算
                        engagement_time = 8
                    elif max_end - max_start >= 10800:  # 最大结束时间比最大开始时间大3小时时,结束时间按最大开始时间计算
                        engagement_time = (max_start - check_in_time) / 3600
                        log.info("超过最大8小时送货时间，工作时长按8小时计算 ")
                    else:
                        engagement_time = (max_end - check_in_time) / 3600
                else:
                    if (max_end - minn_start) >= 28800:
                        engagement_time = 8
                    elif max_end - max_start >= 10800:
                        engagement_time = (max_start - minn_start) / 3600
                        log.info("超过最大8小时送货时间，工作时长按8小时计算 ")
                    else:
                        engagement_time = (max_end - minn_start) / 3600
                minn_start_time = datetime.datetime.fromtimestamp(minn_start).strftime("%Y-%m-%d %H:%M:%S")
                max_start_time = datetime.datetime.fromtimestamp(max_start).strftime("%Y-%m-%d %H:%M:%S")
                minn_end_time = datetime.datetime.fromtimestamp(minn_end).strftime("%Y-%m-%d %H:%M:%S")
                max_end_time = datetime.datetime.fromtimestamp(max_end).strftime("%Y-%m-%d %H:%M:%S")
                log.info(
                    f"最小开始时间：{minn_start_time}, 最大开始时间：{max_start_time}, "
                    f"最小完成时间{minn_end_time}, 最大完成时间：{max_end_time}")
                log.info(f"派车单配送时长：{engagement_time:.2f}小时")
                return engagement_time
            else:
                log.info("配送时间数据不全，无法计算工作时长")
                return 0
        else:
            log.info("配送时间数据不全，无法计算工作时长")
            return 0

    def get_driver_info(self, delivery_plan_id, group_point_id, info="type"):
        """
        获取司机信息
        @param delivery_plan_id: 计划ID
        @param group_point_id: 路线号
        @param info: 司机类型
        @return:
        """
        sql = f"""SELECT {info} FROM vm_driver WHERE driver_user_id = (select driver_user_id FROM
        vm_dispatch  where delivery_plan_id = {delivery_plan_id} and 
        group_point_id = {group_point_id} and status = "A")"""
        try:
            results = self.cd.query_data(sql)[0]
            return results[0]
        except TypeError:
            return False

    def driver_clock_time(self, driver_user_id, delivery_date):
        """
        司机打卡时间
        :param driver_user_id: 司机ID
        :param delivery_date:  配送日期
        :return:
        """
        sql = f"""SELECT time_minute FROM tms.dms_driver_timecards WHERE driver_user_id = {driver_user_id} AND entry_date ='{delivery_date}'"""
        results = self.cd.query_data(sql)
        sum_result = sum(result[0] for result in results)
        clock_time = sum_result / 60
        log.info(f'司机:{driver_user_id}在日期:{delivery_date}的打卡时长为:{clock_time:.2f}小时')
        return clock_time

    def get_delivery_type(self, delivery_plan_id=6405, group_point_id=1):
        """
        查询配送单类型
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"""select id, payment_version, delivery_type, driver_type, driver_user_id, delivery_date  FROM  vm_dispatch  where delivery_plan_id = {delivery_plan_id}
        AND group_point_id = {group_point_id} AND status = 'A'"""
        results = self.cd.query_data(sql)
        log.info(f"派车单ID：{results[0][0]},运费版本：{results[0][1]},司机类型：{results[0][3]}")
        return results[0]

    def get_delivery_zipcode(self, delivery_plan_id, group_point_id):
        """
        查询派车单zipcode
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        sql = f"SELECT p.zipcode FROM vm_delivery_plan_point p WHERE delivery_plan_id={delivery_plan_id} " \
              f"AND p.group_point_id = {group_point_id}"
        results = self.cd.query_data(sql)
        zipcodes = []
        for i in results:
            zipcodes.append(int(i[0]))
        return zipcodes

    def get_new_york_zipcode(self):
        """
        获取纽约zipcode
        @return:
        """
        sql = "select zipcode from weee_comm.gb_zipcode_area where sales_org_id = 7 and county = 'New York'"
        results = self.cd.query_data(sql)
        new_york_zipcodes = []
        for i in results:
            new_york_zipcodes.append(int(i[0]))
        return new_york_zipcodes

    # ***********************订单相关查询***********************
    def get_deal_id_by_order(self, order_id):
        """
        根据order id查询帖子相关数据
        :param order_id:
        :return:
        """
        sql = f"SELECT deal_id FROM weee_comm.gb_order  WHERE id = {order_id} OR parent_order_id = {order_id}"
        result = self.cd.query_data(sql)[0][0]
        log.info(f'根据order id:{order_id}查询帖子deal_id:{result}')
        return result

    def get_deal_status(self, deal_id):
        """
        查询帖子状态信息
        :param deal_id:
        :return:
        """
        sql = f"SELECT status FROM weee_comm.gb_deal gd WHERE gd.id ={deal_id};"
        result = self.cd.query_data(sql)[0][0]
        log.info(f'查询帖子状态信息:{result}')
        return result

    def get_deal_delivery_date(self, deal_id):
        """
        查询帖子配送日期
        :param deal_id:
        :return:
        """
        sql = f"SELECT delivery_date FROM weee_comm.gb_deal gd WHERE gd.id ={deal_id};"
        result = self.cd.query_data(sql)[0][0].strftime('%Y-%m-%d')
        log.info(f'查询帖子配送日期:{result}')
        return result

    def get_deal_info(self, deal_id, info='*'):
        """
        查询帖子信息
        :param deal_id:
        :param info:
        :return:
        """
        sql = f"SELECT {info} FROM weee_comm.gb_deal gd WHERE gd.id ={deal_id};"
        result = self.cd.query_data(sql)[0]
        log.info(f'查询帖子{info}信息:{result}')
        return result

    def update_deal_status(self, deal_id, status='C'):
        """
        更新帖子状态信息,
        :param deal_id:
        :param status: A:打开, C关闭
        :return:
        """
        sql = f'UPDATE weee_comm.gb_deal SET `status` = "{status}" WHERE id ={deal_id}'
        self.cd.update_data(sql)
        log.info(f'更新帖子状态信息成功置为:{status}')

    def get_inventory_info(self, sales_org_id, info='*'):
        """
        查询仓库信息
        :param sales_org_id:
        :param info:
        :return:
        """
        sql = f"SELECT {info} FROM weee_comm.gb_inventory WHERE sales_org_id ={sales_org_id};"
        result = self.cd.query_data(sql)[0]
        log.info(f'查询Inventory的{info}信息:{result}')
        return result

    def get_deal_order_ids(self, deal_id):
        """
        获取帖子下面所有可下发的订单ID
        :param deal_id:
        :return:
        """
        sql = f"SELECT id FROM weee_comm.gb_order WHERE deal_id = {deal_id} AND status ='P'"
        res = self.cd.query_data(sql)
        if res:
            result = tuple(map(lambda x: x[0], res))
            log.info(f'获取帖子{deal_id}下面所有状态为P的订单ID:{result}')
            return result
        else:
            return False

    def update_deal_order_status(self, deal_id):
        """
        将帖子下面所有不为P的订单更新为"X'
        :param deal_id:
        :return:
        """
        sql = f"UPDATE weee_comm.gb_order SET status ='X' WHERE deal_id ={deal_id} AND status !='P'"
        self.cd.update_data(sql)
        log.info(f'将帖子{deal_id}下面所有不为P的订单更新为"X')

    def get_fpo_order_config_id(self, order_id):
        """
        获取下发配置ID
        :param order_id:
        :return:
        """
        sql = f"SELECT fo.fpo_delivery_config_id FROM weee_comm.fpo_order fo WHERE fo.so_order_id ={order_id};"
        result = self.cd.query_data(sql)[0][0]
        log.info(f'获取FPO下发的fpo_delivery_config_id:{result}')
        return result

    def get_last_push_time(self, config_id):
        """
        获取fpo_delivery_config表中的last_push_time
        :param config_id:
        :return:
        """
        sql = f"SELECT fdc.last_push_time  FROM weee_comm.fpo_delivery_config fdc WHERE fdc.id ={config_id}"
        result = self.cd.query_data(sql)[0][0]
        log.info(f'获取fpo_delivery_config表中的last_push_time:{result}')
        return result

    def update_last_push_time(self, config_id):
        """
        更新fpo_delivery_config表中的last_push_time
        :param config_id:
        :return:
        """
        sql1 = f"SELECT fdc.last_push_time  FROM weee_comm.fpo_delivery_config fdc WHERE fdc.id ={config_id}"
        last_push_time = self.cd.query_data(sql1)[0][0]
        log.info(f'获取fpo_delivery_config表中的last_push_time:{last_push_time}')
        new_last_push_time = int(last_push_time) - 300

        sql2 = f"update weee_comm.fpo_delivery_config set last_push_time={new_last_push_time} WHERE id ={config_id}"
        log.info(f'更新fpo_delivery_config表中的last_push_time:{new_last_push_time}')
        self.cd.update_data(sql2)
        time.sleep(5)

    def query_order_sku(self, order_id, storage_type):
        """
        查询订单sku
        """
        sql = f"""
                SELECT gp.id,gp.title,gp.storage_type FROM weee_comm.gb_product gp 
                JOIN weee_comm.gb_order_product gop ON gop.product_id = gp.id 
                WHERE gop.order_id ={order_id} AND gp.storage_type ='{storage_type}' limit 1;"""
        result = self.cd.query_data(sql)
        log.info(f'订单{storage_type}类型SKU:{result}')
        return result

    def get_delivery_region_id(self, config_id):
        """
        获取fpo_delivery_config表中的delivery_region_id
        :param config_id:
        :return:
        """
        sql = f"SELECT fdc.delivery_region_id FROM weee_comm.fpo_delivery_config fdc WHERE fdc.id ={config_id}"
        result = self.cd.query_data(sql)[0][0]
        log.info(f'获取fpo_delivery_config表中的delivery_region_id:{result}')
        return result

    def get_processing_warehouse_number(self, config_id):
        """
        获取fpo_delivery_config表中的processing_warehouse_number
        :param config_id:
        :return:
        """
        sql = f"SELECT fdc.processing_warehouse_number  FROM weee_comm.fpo_delivery_config fdc WHERE fdc.id ={config_id}"
        result = self.cd.query_data(sql)[0][0]
        log.info(f'获取fpo_delivery_config表中的processing_warehouse_number:{result}')
        return result

    def update_last_order_time(self, config_id, last_order_time=None):
        """
        更新last_order_time
        :param config_id:
        :param last_order_time:
        :return:
        """
        if not last_order_time:
            last_order_time = int(time.time())
        sql = f"UPDATE weee_comm.fpo_delivery_config SET last_order_time ={last_order_time} WHERE id ={config_id}"
        self.cd.update_data(sql)
        log.info(f'更新fpo_delivery_config表中的last_order_time为:{last_order_time}')

    def get_last_order_time(self, config_id):
        """
        获取last_order_time
        :param config_id:
        :return:
        """
        sql = f"select last_order_time from weee_comm.fpo_delivery_config WHERE id ={config_id}"
        result = self.cd.query_data(sql)[0][0]
        log.info(f'获取fpo_delivery_config表中的last_order_time为:{result}')

    def get_delivery_id_by_order_id(self, order_id):
        """
        根据Orderid获取批次ID
        :param order_id:
        :return:
        """
        sql = f'SELECT delivery_id, delivery_date, sales_org_id FROM weee_comm.gb_invoice WHERE id=(SELECT invoice_id FROM weee_comm.gb_order WHERE id = {order_id})'
        result = self.cd.query_data(sql)[0]
        log.info(f'根据Orderid{order_id}获取批次ID:{result[0]}, 发货时间为:{result[1]}, 销售组织ID为:{result[2]}')
        return result[0]

    def get_delivery_type_by_delivery_id(self, delivery_id):
        """
        根据批次id获取配送类型
        :param delivery_id: 批次ID
        :return:
        """
        sql = f'SELECT delivery_type FROM weee_comm.gb_delivery WHERE id = {delivery_id}'
        result = self.cd.query_data(sql)[0][0]
        log.info(f'根据批次id:{delivery_id}获取配送类型:{result}')
        return result

    def order_data_process(self, so_order_id):
        """
        根据order_id获取订单相关信息,全量状态为P的订单&取消已完成订单
        :param so_order_id:
        :return:
        """
        sql1 = f"""SELECT deal_id FROM weee_comm.gb_order  WHERE id = {so_order_id}"""
        res1 = self.cd.query_data(sql1)
        log.info(f'团购贴deal_id:{res1[0][0]}')

        sql2 = f"""SELECT id FROM weee_comm.gb_order  WHERE deal_id = {res1[0][0]}"""
        res2 = self.cd.query_data(sql2)
        order_list = []
        for record in res2:
            order_list.append(record[0])
        log.info(f'已支付P全量订单list:{order_list}')
        # deal_id deal_delivery_date帖子
        sql3 = f"""SELECT delivery_date FROM weee_comm.gb_deal WHERE id = {res1[0][0]}"""
        res3 = self.cd.query_data(sql3)[0][0]
        log.info(f'订单团购贴delivery_date:{res3}')
        # 取消异常订单
        sql4 = f"""UPDATE weee_comm.gb_order SET  status ='X' WHERE deal_id ={res1[0][0]} AND status ='F'"""
        self.cd.update_data(sql4)

        return res1, res3

    def get_order_fpo_config_info(self, order_id, info=None):
        """
        获取fpo_delivery_config 各字段信息
        :param config_id:
        :return:
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = (f"""
                SELECT DISTINCT {info_str} FROM weee_comm.fpo_order fo 
                JOIN weee_comm.fpo_delivery_config fdc ON fdc.id = fo.fpo_delivery_config_id 
                JOIN weee_comm.fpo_region_warehouse_mapping frwm ON frwm.delivery_region_id  = fdc.delivery_region_id
                WHERE fo.so_order_id ={order_id};""")
        result = self.cd.query_data(sql)
        if not result:
            log.info(f'fpo订单信息:{order_id} 无结果')
            return None
        else:
            log.info(f'fpo订单信息:{order_id}: {result}')
            return result

    def clean_fpo_data(self, delivery_id):
        """
        重置FPO数据
        :param delivery_id:
        :return:
        """
        sql1 = f"""UPDATE weee_comm.fpo_order JOIN weee_comm.gb_order ON weee_comm.fpo_order.so_order_id = weee_comm.gb_order.id JOIN weee_comm.gb_invoice ON weee_comm.gb_order.invoice_id = weee_comm.gb_invoice.id SET weee_comm.fpo_order.fpo_status = 0 WHERE delivery_id ={delivery_id}"""
        sql2 = f"""DELETE weee_comm.wms_order_item.* FROM weee_comm.wms_order_item JOIN weee_comm.wms_order ON weee_comm.wms_order.id = weee_comm.wms_order_item.order_id JOIN weee_comm.gb_invoice ON weee_comm.wms_order.invoice_id = weee_comm.gb_invoice.id WHERE delivery_id = {delivery_id}"""
        sql3 = f"""DELETE weee_comm.wms_order.* FROM weee_comm.wms_order JOIN weee_comm.gb_invoice ON weee_comm.wms_order.invoice_id = weee_comm.gb_invoice.id WHERE delivery_id ={delivery_id}"""
        self.cd.update_data(sql1)
        self.cd.update_data(sql2)
        self.cd.update_data(sql3)
        log.info("FPO数据重置完成")
        return

    def update_delivery_creater(self, delivery_id):
        sql1 = f"""UPDATE weee_comm.gb_delivery SET rec_creator_id = 7875714 WHERE id ={delivery_id}"""
        self.cd.update_data(sql1)

    def delete_delivery(self, delivery_id):
        sql1 = f"""update weee_comm.gb_invoice set status = 'X' where delivery_id = {delivery_id}"""
        sql2 = f"""UPDATE weee_comm.gb_order o JOIN weee_comm.gb_order_product op ON op.order_id = o.id SET o.invoice_id = NULL WHERE op.original_delivery_id = {delivery_id}"""
        sql3 = f"""UPDATE weee_comm.gb_order_product SET delivery_id = NULL, original_delivery_id = NULL WHERE original_delivery_id = {delivery_id}"""
        sql4 = f"""UPDATE weee_comm.gb_delivery SET status = 'X' WHERE id = {delivery_id}"""
        sql5 = f"""UPDATE weee_comm.gb_order_product SET delivery_id = original_delivery_id WHERE original_delivery_id = {delivery_id} AND (delivery_id = - 1 OR delivery_id IS NULL );"""
        self.cd.update_data(sql1)
        self.cd.update_data(sql2)
        self.cd.update_data(sql3)
        self.cd.update_data(sql4)
        self.cd.update_data(sql5)
        return

    def check_downorder_erp(self, delivery_id):
        sql = f"""SELECT
                    weee_comm.gb_invoice.id AS invoice_no,
                    weee_comm.wms_order_item.product_id AS item_number,
                    sum(wms_order_item.quantity) AS itemQuantity
                    from
                    weee_comm.gb_invoice
                    join weee_comm.wms_order ON
                    weee_comm.wms_order.invoice_id = weee_comm.gb_invoice.id
                    JOIN weee_comm.wms_order_item ON
                    weee_comm.wms_order_item.order_id = weee_comm.wms_order.id
                    join weee_comm.gb_product ON
                    weee_comm.gb_product.id = weee_comm.wms_order_item.product_id
                    join weee_comm.gb_order ON
                    weee_comm.gb_order.id = weee_comm.gb_invoice.ref_order_id
                    where
                    weee_comm.gb_invoice.delivery_id = {delivery_id}
                    # COALESCE(weee_comm.gb_invoice.delivery_id, '') = COALESCE({delivery_id}, '')
                    AND weee_comm.gb_invoice.STATUS != 'X'
                    group by
                    weee_comm.wms_order.id,
                    weee_comm.gb_invoice.id,
                    weee_comm.gb_invoice.delivery_id,
                    weee_comm.wms_order.inventory_id,
                    weee_comm.gb_order.rec_creator_id,
                    weee_comm.gb_order.phone,
                    weee_comm.gb_order.addr_address,
                    weee_comm.wms_order_item.product_id
                    order by
                    weee_comm.wms_order_item.product_id,
                    weee_comm.gb_invoice.id"""

        result = self.cd.query_data(sql)
        log.info(f'erp_order_qty:{result}')
        return result

    def get_dispatch_order_ids(self, dispatch_id):
        """
        获取派车单下所有的order_id
        :param dispatch_id:
        :return:
        """
        sql = f"""select gb_order.id from 
            vm_dispatch
            join vm_delivery_plan on vm_delivery_plan.id  = vm_dispatch.delivery_plan_id
            join vm_delivery_plan_mapping on vm_delivery_plan_mapping.delivery_plan_id = vm_delivery_plan.id
            join weee_comm.gb_delivery_order on gb_delivery_order.delivery_id = vm_delivery_plan_mapping.delivery_id
            join weee_comm.gb_order on gb_order.id  = gb_delivery_order.order_id
            WHERE vm_dispatch.id = {dispatch_id}"""
        result = self.cd.query_data(sql)
        if len(result) > 1:
            order_ids = tuple(item[0] for item in result)
        else:
            order_ids = result[0][0]
        log.info(f'get_dispatch_order_ids:{order_ids}')
        return order_ids

    def get_fbw_region_id(self, region_id):
        """
        获取对应FBW的RegionID
        :param region_id:
        :return:
        """
        sql = f"""SELECT template_region_id FROM weee_comm.fpo_template_region_mapping WHERE grocery_region_id = {region_id}"""
        res = self.cd.query_data(sql)
        if res:
            result = res[0][0]
            log.info(f'对应Region:{region_id}的FBW RegionID{result}')
            return result
        else:
            return False

    def get_tracking_info(self, dispatch_id):
        """
        获取对应派车单的打包信息
        :param dispatch_id:
        :return:
        """
        sql = f"""SELECT 
                    dpp.seq, 
                    tp.tracking_num, 
                    tp.storage_type 
                FROM 
                    tms.vm_delivery_plan_point dpp 
                INNER JOIN 
                    tms.vm_dispatch d 
                ON 
                    dpp.delivery_plan_id = d.delivery_plan_id 
                AND 
                    dpp.group_point_id = d.group_point_id
                LEFT JOIN
                    tms.tms_package tp ON dpp.id = tp.stop_id
                WHERE
                    d.id = {dispatch_id}
                    ORDER BY dpp.seq ASC;"""
        result = self.cd.query_data(sql)
        if result:
            log.info(f'获取对应派车单{dispatch_id}的打包信息为{result}')
            return result
        else:
            return False

    def get_dispatch_delivery_time(self, dispatch_id=None, delivery_plan_id=None, group_point_id=None):
        """
        获取路线距离
        :param dispatch_id: 派车单ID
        :param delivery_plan_id: 计划ID
        :param group_point_id: 路线号
        :return:
        """
        if dispatch_id:
            sql = f"""SELECT actual_start_time,actual_finish_time FROM tms.vm_dispatch WHERE id = {dispatch_id}"""
        else:
            sql = f"""
            SELECT actual_start_time,actual_finish_time FROM tms.vm_dispatch WHERE delivery_plan_id ={delivery_plan_id} 
            AND group_point_id ={group_point_id}"""
        result = self.cd.query_data(sql)[0]
        log.info(f"开始时间：{result[0]},结束时间：{result[1]}")
        return result

    def update_dispatch_date_by_plan_id(self, plan_id, delivery_date):
        """
        更新排车计划下的派车单日期
        :param plan_id:
        :param delivery_date:
        :return:
        """
        sql = f"UPDATE tms.vm_dispatch SET delivery_date = '{delivery_date}' WHERE delivery_plan_id ={plan_id} AND status ='A'"
        self.cd.update_data(sql)
        log.info(f"更新plan_id{plan_id}下的派车单日期为{delivery_date}")

    def fpo_order_extend_sql(self, so_order_id, delivery_time_id, external_start_time, external_end_time):
        sql = f"""INSERT INTO weee_comm.fpo_order_extend
            (so_order_id, extend_type, delivery_time_id, external_start_time, external_end_time, rec_creator_id, rec_create_time, rec_update_id, rec_update_time)
            VALUES({so_order_id}, 1, {delivery_time_id}, '{external_start_time}', '{external_end_time}', 0, '2024-03-14 07:30:00', 0, '2024-03-14 07:30:00');"""
        log.info(sql)
        self.cd.update_data(sql)

    def get_dms_delivery_window(self, order_id):
        sql1 = f"""SELECT delivery_time_id  FROM weee_comm.gb_order WHERE id ={order_id}"""
        result1 = self.cd.query_data(sql1)[0][0]
        sql2 = f"""SELECT x.external_start_time,x.external_end_time,x.is_free FROM tms.dms_delivery_window x WHERE x.id ={result1}"""
        result2 = self.cd.query_data(sql2)
        log.info(result2)
        if result2:
            result2 = result2[0]
        else:
            result2 = [0, 0, 1]
        return result1, result2

    def delete_course_and_task(self, title='autoTest'):
        del_task_sql = f"""
        DELETE FROM tms.dms_course_task WHERE course_id IN (SELECT id FROM tms.dms_course WHERE title like '{title}%');
        """
        del_course_sql = f"""
        DELETE FROM tms.dms_course WHERE title like '{title}%';
        """
        self.cd.update_data(del_task_sql)
        self.cd.update_data(del_course_sql)

    def update_driver_create_time(self, driver_user_id, offset_hour=0):
        update_time_sql = f"""
        UPDATE vm_driver SET rec_create_time = (select DATE_ADD(NOW() , INTERVAL {offset_hour} hour)) WHERE  driver_user_id  = '{driver_user_id}';;
        """
        self.cd.update_data(update_time_sql)

    def update_driver_time_card_log(self, driver_user_id, my_time):
        update_time_sql = f"""
                UPDATE dms_driver_time_card_action_log SET action_local_date_time = '{my_time}' WHERE driver_user_id={driver_user_id} order by id DESC limit 1;;
                """
        self.cd.update_data(update_time_sql)

    def delete_driver_time_card_log(self, driver_user_id, my_date):
        update_time_sql = f"""
                DELETE FROM dms_driver_time_card_action_log WHERE driver_user_id={driver_user_id} and time_card_date='{my_date}';
                """
        self.cd.update_data(update_time_sql)

    def get_driver_user_id_by_dispatch(self, dispatch_id):
        get_user_sql = f"""
                SELECT driver_user_id from vm_dispatch WHERE id  in ({dispatch_id}) and status = 'A';
                """
        res = self.cd.query_data(get_user_sql)
        return res[0][0]

    def get_tms_config_value(self, key):
        get_user_sql = f"""
                SELECT config_value from tms_config tc where config_key = '{key}';
                """
        res = self.cd.query_data(get_user_sql)
        return res[0][0]

    def get_route_not_loadable(self):
        get_dispatch_id_sql = F"SELECT group_point_id,id,delivery_plan_id from vm_dispatch WHERE status = 'A' AND dispatch_type !='G' AND route_status is NULL order by delivery_date  desc limit 1;"
        res = self.cd.query_data(get_dispatch_id_sql)
        return res[0]

    def get_start_end_mileage_distance(self, dispatch_id):
        get_start_end_mileage_distance_sql = f"""
                SELECT start_mileage_distance,end_mileage_distance from tms_dispatch_mileage_record WHERE dispatch_id  ={dispatch_id};
                """
        res = self.cd.query_data(get_start_end_mileage_distance_sql)
        return res[0]

    def get_slot_inventory_info(self, dispatch_id):
        inventory_info_sql = f"""
                SELECT latitude,longitude from tms_address ta WHERE ta.rec_id  in (SELECT address_id FROM tms_dispatch_task WHERE dispatch_id = {dispatch_id} and task_type = 'inventory');;
                """
        res = self.cd.query_data(inventory_info_sql)
        return res[0]

    def get_sub_dispatch_id(self, group_dispatch_id):
        """
        获取大路线下的子路线ID
        :param group_dispatch_id: Flex大路线ID
        :return:
        """
        sql = f"""SELECT sub_dispatch_id FROM tms.vm_sub_group_dispatch_mapping WHERE group_dispatch_id ={group_dispatch_id}"""

        result = self.cd.query_data(sql)
        log.info(f"获取大路线{group_dispatch_id}下的子路线ID为{result}")
        return result

    def get_pick_up_location_info(self, sub_region_id):
        """
        获取取货点信息
        :param sub_region_id:
        :return:
        """
        sql = f"""SELECT start_location_address,latitude,longitude FROM tms.tms_delivery_sub_region WHERE id ={sub_region_id}"""
        result = self.cd.query_data(sql)[0]
        log.info(f"获取sub_region_id: {sub_region_id}的取货点信息为{result}")
        return result

    def get_capacity(self, sub_region_id, day):
        """
        获取指定region下指定日期的司机运力
        :param sub_region_id:
        :param day
        :retrun
        """
        sql = F"SELECT driver_type,sum(quantity * capacity),case when driver_type in ('F','P') then COUNT(1) else quantity end as sum FROM dm_schedule_capacity where delivery_date = '{day}' and sub_region_id ={sub_region_id} AND schedule_status =1 GROUP BY driver_type;"
        result = self.cd.query_data(sql)
        log.info(f"获取sub_region_id: {sub_region_id}的{day}的dm_schedule_capacity是{result}")
        return result

    def get_info_by_dispatch_id(self, info, dispatch_id):
        """
        获取派车单信息
        :param info:
        :param dispatch_id:
        :return:
        """
        sql = f"SELECT {info} FROM vm_dispatch WHERE id = {dispatch_id}"
        result = self.cd.query_data(sql)[0][0]
        log.info(f"获取dispatch_id: {dispatch_id}的{info}是{result}")
        return result

    def update_sub_region_config(self, sub_region_id, config_key, config_value):
        """
        更新sub_region_config配置
        :param sub_region_id:
        :param config_key:
        :param config_value:
        :return:
        """
        sql = f"UPDATE tms.tms_delivery_sub_region_config SET {config_key} = {config_value} WHERE sub_region_id = {sub_region_id}"
        self.cd.update_data(sql)
        log.info(f"更新sub_region_id: {sub_region_id}的{config_key}为{config_value}")

    def get_driver_capacity(self, driver_user_id, day):
        """
        获取司机在指定日期的运力
        :param driver_user_id:
        :param day:
        :return:
        """
        sql = f"SELECT sub_region_id, capacity FROM dm_schedule_capacity WHERE driver_user_id = {driver_user_id} AND delivery_date = '{day}' AND schedule_status = 1"
        result = self.cd.query_data(sql)[0]
        log.info(f"获取driver_user_id: {driver_user_id}的{day}的dm_schedule_capacity是{result}")
        return result


if __name__ == '__main__':
    tms = TmsDB()
    fee_config = tms.get_sub_region_name(sub_region_id=3)

    print(fee_config)
