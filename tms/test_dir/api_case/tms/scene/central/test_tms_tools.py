# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_tms_tools.py
@Description    :  
@CreateTime     :  2024/5/10 17:56
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/5/10 17:56
"""
import weeeTest

from tms.test_dir.api.tms.tms import tms
from wms.test_dir.api.wms.wms import wms


class TestTools(weeeTest.TestCase):
    """
    测试工具类(不参与测试执行)
    """

    @weeeTest.mark.list('tools', 'checkEnv')
    def test_check_env(self):
        """
        框架环境检查
        """
        tms.login()
        wms.login()
        tms.tms_db.get_user_id(name='k001')
        wms.wms_db.get_wh_restock_task_info()
        tms.central.route_plan.get_route_region_id()

    @weeeTest.mark.list('tools', 'delete_draft')
    def test_delete_draft(self):
        """
        删除plan状态的draft
        """
        tms.login()
        tms.delete_draft(delivery_plan_id=19465)

    @weeeTest.mark.list('tools', 'cancel_draft')
    def test_cancel_draft(self):
        """
        stop正在运行状态的draft
        """
        tms.login()
        tms.cancel_draft(delivery_plan_id=19152)

    @weeeTest.mark.list('tools', 'delete_delivery')
    def test_delete_delivery_data(self):
        """
        重置发货计划数据
        """
        tms.login()
        delivery_date = '2024-08-30'
        sub_region_ids = [3]
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)

    @weeeTest.mark.list('tools', 'address_pre_check')
    def test_address_pre_check(self):
        """
        地址预校验
        """
        tms.login()
        delivery_date = '2022-12-21'
        region_ids = [1]
        plan_id = []
        for region_id in region_ids:
            sub_region_ids = tms.tms_db.get_sub_region_ids(region_id)
            delivery_plan_res = tms.central.route_plan.get_region_dispatch_list(sub_region_ids, delivery_date)
            delivery_plan_info = tms.util.get_response_values(delivery_plan_res, 'data')[0]
            if len(delivery_plan_info) > 0:
                delivery_plan_list = tms.util.get_response_values(delivery_plan_res, 'id')[0]
                if isinstance(delivery_plan_list, int):
                    res = tms.central.route_plan.address_pre_check(delivery_plan_id=delivery_plan_list)
                    if len(res.get('object').get('riskAddresses')) > 0 or len(
                            res.get('object').get('errorAddresses')) > 0:
                        plan_id.append(delivery_plan_list)
                elif isinstance(delivery_plan_list, list):
                    for delivery_plan_id in delivery_plan_list:
                        res = tms.central.route_plan.address_pre_check(delivery_plan_id=delivery_plan_id)
                        if len(res.get('object').get('riskAddresses')) > 0 or len(
                                res.get('object').get('errorAddresses')) > 0:
                            plan_id.append(delivery_plan_id)
        tms.util.print_log(f"包含风险地址的派车计划:{plan_id}")

    @weeeTest.mark.list('tools', 'split_rtg_route')
    def test_v12_ic_complaince_new_route(self):
        """
        v12算法IC Complaince新增Flex路线测试
        """
        # 获取发货计划
        tms.login()
        draft_id = 16239
        delivery_plan_id = tms.tms_db.get_delivery_plan_id_by_draft(draft_id)
        sub_region_id = tms.tms_db.get_sub_region_id_by_delivery_plan(delivery_plan_id)
        flex_cap = tms.tms_db.get_tms_algorithm_config(sub_region_id=sub_region_id, config_key='Flex cap')
        mini_van_cap = tms.tms_db.get_tms_algorithm_config(sub_region_id=sub_region_id,
                                                           config_key='Flex Mini van cap')
        # 1.获取配送点信息
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info = tms.util.get_response_values(draft_routes_res, 'object')
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                   delivery_plan_id=delivery_plan_id,
                                                                   route_nums=draft_routes_info[0][:1])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'stop_id')
        # 1.新增 Mega_Route路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0],
                                            stop_ids=draft_points_info[1][-int(mini_van_cap):],
                                            draft_id=draft_id, driver_id=1263001)
        tms.check_route_change_result(draft_id)

        # 2.获取配送点信息
        draft_routes_res1 = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info1 = tms.util.get_response_values(draft_routes_res1, 'object')
        draft_points_res1 = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                    delivery_plan_id=delivery_plan_id,
                                                                    route_nums=draft_routes_info1[0][:1])
        draft_points_info1 = tms.util.get_response_values(draft_points_res1, 'route_rec_id[0]', 'stop_id')

        # 2.新增 Mini_Route路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info1[0],
                                            stop_ids=draft_points_info1[1][-int(flex_cap):],
                                            draft_id=draft_id, driver_id=1263002)
        tms.check_route_change_result(draft_id)

        # 校验路线新增成功
        draft_routes_res2 = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info2 = tms.util.get_response_values(draft_routes_res2, 'object')

    @weeeTest.mark.list('tools', 'flex_route')
    def test_flex_route(self):
        """
        Flex路线创建
        """
        tms.login(driver_email='<EMAIL>')
        delivery_plan_id = 20511
        sub_region_id = tms.tms_db.get_sub_region_id_by_delivery_plan(delivery_plan_id=delivery_plan_id)
        sub_region_ids = [sub_region_id]
        flex_cap = 2
        mini_van_cap = 4
        tms.tms_db.reset_delivery_plan(delivery_plan_id)
        tms.tms_db.update_flex_cap(suv_cap=flex_cap, minivan_cap=mini_van_cap, sub_region_id=sub_region_ids[0])
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_process_for_draft_run(delivery_plan_id)
        draft_info = tms.util.get_response_values(draft_info_res, f'delivery_area_stop_num_setting')[0][-1]
        driver_res = tms.central.route_plan.get_driver_info_2(delivery_plan_id=delivery_plan_id,
                                                              stop_num_setting=draft_info)
        # 获取算法执行参数
        sub_region_name = tms.tms_db.get_sub_region_name(sub_region_ids[0])
        driver_num_info = tms.util.get_driver_nums(driver_res)
        # 算法执行
        draft_id = tms.central.route_plan.run_ic_complaince_v18(delivery_plan_id=delivery_plan_id,
                                                                sub_region_id=sub_region_ids[0],
                                                                sub_region_name=sub_region_name,
                                                                mini_van_stop=driver_num_info['mini_van_stop'],
                                                                mini_van_num=driver_num_info['mini_van_num'],
                                                                w2_stop=driver_num_info['w2_stop'],
                                                                w2_num=driver_num_info['w2_num'],
                                                                df_stop=driver_num_info['df_stop'],
                                                                df_num=driver_num_info['df_num'],
                                                                suv_stop=driver_num_info['suv_stop'],
                                                                suv_num=driver_num_info['suv_num'])['object'][
            'draft_id']
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 1.获取配送点信息
        route_info = tms.tms_db.get_route_info(draft_id=draft_id)
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info = tms.util.get_response_values(draft_routes_res, 'object')
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                   delivery_plan_id=delivery_plan_id,
                                                                   route_nums=[route_info[0][1]])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'stop_id')
        # 1.新增 FlexSUV路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0],
                                            stop_ids=draft_points_info[1][-int(flex_cap + 1):-1],
                                            draft_id=draft_id, driver_id=11947547)
        tms.check_route_change_result(draft_id)

        # 2.获取配送点信息
        draft_routes_res1 = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info1 = tms.util.get_response_values(draft_routes_res1, 'object')
        assert max(draft_routes_info[0]) + 1 == max(draft_routes_info1[0])

        draft_points_res1 = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                    delivery_plan_id=delivery_plan_id,
                                                                    route_nums=[route_info[0][1]])
        draft_points_info1 = tms.util.get_response_values(draft_points_res1, 'route_rec_id[0]', 'stop_id')

        # 2.新增 Minivan路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info1[0],
                                            stop_ids=draft_points_info1[1][-int(mini_van_cap + 1):-1],
                                            draft_id=draft_id, driver_id=24012401)
        tms.check_route_change_result(draft_id)

        # 3.获取配送点信息
        draft_routes_res2 = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info2 = tms.util.get_response_values(draft_routes_res2, 'object')
        assert max(draft_routes_info1[0]) + 1 == max(draft_routes_info2[0])

        draft_points_res2 = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                    delivery_plan_id=delivery_plan_id,
                                                                    route_nums=[route_info[0][1]])
        draft_points_info2 = tms.util.get_response_values(draft_points_res2, 'route_rec_id[0]', 'stop_id')

        # 3.新增 IC路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info2[0],
                                            stop_ids=draft_points_info2[1][-4:-1],
                                            draft_id=draft_id, driver_id=13341570)
        tms.check_route_change_result(draft_id)

        #  拆分路线再次应用
        tms.tms_db.update_delivery_zone(zone_value='0,0,0', sub_region_id=sub_region_ids[0])
        # 等待路线数据异步计算完成
        tms.util.wait(30)
        tms.central.route_plan.split_flex_route(draft_id, check_result=True)
        tms.create_dispatch_check(draft_id)

        # 设置抢单配置
        time_list = tms.util.calculate_time(time_interval=15)
        tms.tms_db.update_flex_config(release_time=time_list[1], pickup_time=time_list[2],
                                      sub_region_id=sub_region_ids[0], rescue_time=time_list[3])
        tms.tms_db.update_dispatch_date_by_plan_id(plan_id=delivery_plan_id,
                                                   delivery_date=tms.util.us_current_date())
        tms.tms_db.delete_driver_activity_log(driver_user_id=10954309)  # f_r1c01  Mini Van
        tms.tms_db.delete_driver_activity_log(driver_user_id=13339585)  # f_r1c03  SUV


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'flex_route', "--junitxml=tests/results.xml"])
