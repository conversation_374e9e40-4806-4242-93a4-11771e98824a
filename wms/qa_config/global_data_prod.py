# 是否是生产环境
is_prod = True
# ---------------------------------------------TMS参数---------------------------------------------
# ---------------------------------------------INV参数---------------------------------------------
# query参数
pantry_inv_query = {"sales_org_id": 10,
                    "zipcode": "77070",
                    "sales_model": "inventory",
                    "region_id": "20",
                    "warehouse_number": "25"
                    }
global_fbw_query = {"sales_org_id": 9,
                    "zipcode": "92069",
                    "product_id": 2116282,
                    "sales_model": "inventory",
                    "product_type": "mkpl_fbw",
                    "region_id": "10",
                    "warehouse_number": "25",
                    "seller_id": 6887}
local_fbw_query = {"sales_org_id": 9,
                   "zipcode": "92069",
                   "product_id": 2209633,
                   "sales_model": "schedule",
                   "product_type": "fbw",
                   "region_id": "10",
                   "warehouse_number": "25",
                   "seller_id": 8668
                   }
global_query = {"sales_org_id": 1,
                "zipcode": "94501",
                "product_id": 2121775,
                "sales_model": "inventory",
                "product_type": "seller",
                "region_id": "0",
                "warehouse_number": None,
                "seller_id": 7611
                }
grocery_inventory_query = {"sales_org_id": 1,
                           "zipcode": "94501",
                           "product_id": 9450,
                           "sales_model": "inventory",
                           "product_type": "normal",
                           "region_id": "5",
                           "warehouse_number": "25"
                           }
grocery_schedule_query = {"sales_org_id": 1,
                          "zipcode": "94501",
                          "product_id": 97078,
                          "sales_model": "schedule",
                          "product_type": "normal",
                          "region_id": "5",
                          "warehouse_number": "7"
                          }

# ---------------------------------------------WMS参数---------------------------------------------
# WMS 账号设置
wms_user_id = "7226349"
wms_user_password = "123456"
user_name = "janine.cheng.1"

#restock
restock = {'warehouse_number': '48','storage_type': '1','item_number': '1495','jobitem_number':'91788'}  # 1 dry; 2 fresh; 3 frozen
restockjob = {'delivery_date': '2025-06-01','configid':2651,'itemlist':['14659', '1829', '91788', '3608']}

#collect extra
collect_extra_data = {"warehouse_number": "48", "location_no": "EIT108", "item_number": "10023", "upc": "2000000010023"}