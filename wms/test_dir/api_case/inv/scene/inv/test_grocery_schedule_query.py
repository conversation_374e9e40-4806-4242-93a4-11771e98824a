import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestGroceryScheduleQuery(weeeTest.TestCase):
    """
    Grocery资源池售卖商品库存相关接口查询
    """
    def setup_class(self):
        # 登录
        self.user_id = wms.wms_login.common_login()[0]
        self.sales_org_id = global_data.grocery_schedule_query['sales_org_id']
        self.zipcode = global_data.grocery_schedule_query['zipcode']
        self.product_id = global_data.grocery_schedule_query['product_id']
        self.sales_model = global_data.grocery_schedule_query['sales_model']
        self.product_type = global_data.grocery_schedule_query['product_type']
        self.region_id = global_data.grocery_schedule_query['region_id']
        self.warehouse_number = global_data.grocery_schedule_query['warehouse_number']
        self.date = wms.util.get_special_date(days=3)
        # 查询可售库存用于后面校验
        self.product_inv = InvDataUtils().assert_inventory_daily_sales(self.sales_org_id, self.date, self.zipcode, self.product_id)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_grocery_schedule_query(self):
        """
        【102897】Grocery资源池售卖商品库存相关接口查询
        """
        # <EC-INV相关查询接口>
        # /ec/inventory/query/v5接口库存查询
        InvDataUtils().assert_inventory_query_v5(self.date, self.zipcode, self.product_id, self.warehouse_number, self.product_inv, self.sales_model)

        # /ec/inventory/query/v4接口库存查询
        InvDataUtils().assert_inventory_query_v4(self.date, self.sales_org_id, self.product_id, self.warehouse_number,
                                                 self.product_inv, self.sales_model)

        # /ec/inventory/query/all/region/inv接口库存查询
        InvDataUtils().assert_product_all_region_inv(self.date, self.product_id, self.region_id, self.product_inv)

        # /ec/inventory/query/product/inv接口库存查询
        InvDataUtils().assert_ec_inventory_query_product_inv(self.date, self.sales_org_id, self.product_id, self.product_inv, self.zipcode,
                                                             self.warehouse_number)

        # /ec/inventory/fulfillment/query接口库存查询
        InvDataUtils().assert_ec_inventory_fulfillment_query(self.date, self.sales_org_id, self.product_id, self.zipcode, self.product_inv)

        # Central-INV相关查询接口
        # query/product/local/inv接口库存查询
        InvDataUtils().assert_local_inv(self.product_id, self.date, self.warehouse_number, self.product_inv)

        # query/local/warehouse/sales/inv接口库存查询
        InvDataUtils().assert_sales_inv(self.sales_org_id, self.date, self.product_id, self.product_inv)

        # query/all/date/v4接口库存查询
        InvDataUtils().assert_all_date_v4(self.sales_org_id, self.date, self.zipcode, self.product_id, self.product_inv)

        # query/sales_org_id/inv接口库存查询
        InvDataUtils().assert_org_inv(self.sales_org_id, self.sales_model, self.product_id)

        # query/all/region/inv接口库存查询
        InvDataUtils().assert_all_region_inv(self.sales_org_id, self.region_id, [self.product_id], self.date, self.product_inv)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

# 资源池售卖库存查询
