# !/usr/bin/python3
# -*- coding: utf-8 -*-

from jsonpath import jsonpath

import weeeTest
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestPantry(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_local_to_pantry(self):
        """
        【102810】商品自营转pantry售卖
        """
        zipcode = 77071
        # zipcode 对应Region
        region_id = "R20"
        product_id = 107268
        houston_info = {"warehouse_number": "20", "location_no": "A2418-4-1"}
        la_info = {"warehouse_number": "25", "location_no": "A1603-1-4"}
        adjust_qty = 1

        # 登录
        user_id = wms.wms_login.common_login()[0]
        # 查询仓库库存
        inv_res = wms.central_inv_api.query_product_local_inv([product_id], [wms.util.get_special_date(days=1)])
        assert jsonpath(inv_res,
                        f'$...inventory_list[?(@.warehouse_number in ["{houston_info["warehouse_number"]}"])].available_qty') == [adjust_qty], f'{houston_info["warehouse_number"]}仓库可售库存不等于1，请检查测试数据'
        assert jsonpath(inv_res,
                        f'$...inventory_list[?(@.warehouse_number in ["{la_info["warehouse_number"]}"])].available_qty') > [0], f'{la_info["warehouse_number"]}仓库无可售库存，请检查测试数据'
        # 清空20仓库的库存（自营）
        # wms.moving_api.clear_inv(la_info["warehouse_number"], user_id, [la_info["location_no"]], operationVirtualLocation=False)
        wms.moving_api.clear_inv(houston_info["warehouse_number"], user_id, [houston_info["location_no"]], operationVirtualLocation=False)

        # 最大重试次数
        max_retries = 24
        # 每次重试等待时间（秒）
        wait_time = 5

        for attempt in range(max_retries):
            # 检查库存平台商品库存
            inv_result = wms.inv_db.query_item_inventory(houston_info["warehouse_number"], product_id)
            if jsonpath(inv_result, "$[*].available_qty") == [0]:
                break
            else:
                print(f"Attempt {attempt + 1} failed. Retrying in {wait_time} seconds...")
                # 等待一段时间后重试
                wms.util.wait(sleep_time=wait_time)

        # 检查库存平台数据库商品库存
        inv_result = wms.inv_db.query_item_inventory(houston_info["warehouse_number"], product_id)
        assert jsonpath(inv_result, "$[*].available_qty") == [0], f"商品{product_id} 在inv_inventory表中库存未清空"

        # 调用Item接口计算商品pantry属性
        wms.ec_item_api.pantry_transform([product_id], zipcode)

        # 最大重试次数
        max_retries = 24
        # 每次重试等待时间（秒）
        wait_time = 5

        for attempt in range(max_retries):
            result = wms.inv_db.query_item_region_mapping(product_id)
            mo_priority = jsonpath(result,
                                   f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{la_info["warehouse_number"]}" && @.sell_mode=="mo")].priority')
            if mo_priority[0] == 1:
                break
            else:
                print(f"Attempt {attempt + 1} failed. Retrying in {wait_time} seconds...")
                # 等待一段时间后重试
                wms.util.wait(sleep_time=wait_time)

        # 查询库存平台表校验售卖优先级
        # wms.util.wait(sleep_time=wait_time)
        result = wms.inv_db.query_item_region_mapping(product_id)
        print(result)
        grocery_priority = jsonpath(result,
                                    f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{houston_info["warehouse_number"]}" && @.sell_mode=="grocery")].priority')
        mo_priority = jsonpath(result, f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{la_info["warehouse_number"]}" && @.sell_mode=="mo")].priority')
        print(grocery_priority)
        print(mo_priority)
        assert mo_priority[0] == 1, "商品自营转pantry售卖失败"
        assert grocery_priority[0] == 2, "商品自营转pantry售卖失败"

        # V5接口校验
        wms.util.wait(sleep_time=6, reason='同步Redis延迟')
        inv_res = wms.central_inv_api.query_product_local_inv([product_id], [wms.util.get_special_date(days=1)])    # 查询仓库库存
        inv_qty = jsonpath(inv_res, f'$...inventory_list[?(@.warehouse_number in ["{la_info["warehouse_number"]}"])].available_qty')[0]
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, la_info["warehouse_number"], inv_qty)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_pantry_to_local(self):
        """
        【102809】商品pantry售卖转自营
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        zipcode = 77071
        # zipcode 对应Region
        region_id = "R20"
        product_id = 107268
        houston_info = {"warehouse_number": "20", "location_no": "A2418-4-1"}
        la_info = {"warehouse_number": "25", "location_no": "A1603-1-4"}
        adjust_qty = 1

        # 查询仓库库存
        inv_res = wms.central_inv_api.query_product_local_inv([product_id], [wms.util.get_special_date(days=1)])
        assert jsonpath(inv_res, f'$...inventory_list[?(@.warehouse_number=="{houston_info["warehouse_number"]}")].available_qty') == [0], f'{houston_info["warehouse_number"]}仓库可售库存不等于0，请检查测试数据'
        assert jsonpath(inv_res, f'$...inventory_list[?(@.warehouse_number=="{la_info["warehouse_number"]}")].available_qty') > [0], f'{la_info["warehouse_number"]}仓库无可售库存，请检查测试数据'

        # 添加20仓库的库存（自营）
        wms.adjust_api.create_batch_inv(
            product_id,
            houston_info["warehouse_number"],
            user_id,
            houston_info["location_no"],
            adjust_qty,
            is_lpn=False,
            pieces_per_pack=1.00,
            expire_dtm=wms.util.get_special_date(days=365),
            receive_date=wms.util.get_special_date(),
        )

        # 最大重试次数
        max_retries = 24
        # 每次重试等待时间（秒）
        wait_time = 5

        for attempt in range(max_retries):
            # 检查库存平台商品库存
            inv_result = wms.inv_db.query_item_inventory(houston_info["warehouse_number"], product_id)
            if jsonpath(inv_result, "$[*].available_qty") == [1]:
                break
            else:
                print(f"Attempt {attempt + 1} failed. Retrying in {wait_time} seconds...")
                # 等待一段时间后重试
                wms.util.wait(sleep_time=wait_time)

        # 检查库存平台数据库商品库存
        inv_result = wms.inv_db.query_item_inventory(houston_info["warehouse_number"], product_id)
        assert jsonpath(inv_result, "$[*].available_qty") == [1], f"商品{product_id} 在inv_inventory表中库存新增失败"

        # 商品库存从无到有会实时通知item计算，将商品转为本地售卖，查询库存平台表校验售卖优先级
        # 最大重试次数
        max_retries = 12
        # 每次重试等待时间（秒）
        wait_time = 5
        for attempt in range(max_retries):
            # 检查库存平台商品库存
            result = wms.inv_db.query_item_region_mapping(product_id)
            grocery_priority = jsonpath(result,
                                        f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{houston_info["warehouse_number"]}" && @.sell_mode=="grocery")].priority')
            if grocery_priority[0] == 1:
                break
            else:
                print(f"Attempt {attempt + 1} failed. Retrying in {wait_time} seconds...")
                # 等待一段时间后重试
                wms.util.wait(sleep_time=wait_time)

        # wms.util.wait(sleep_time=60)
        result = wms.inv_db.query_item_region_mapping(product_id)
        grocery_priority = jsonpath(result,
                                    f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{houston_info["warehouse_number"]}" && @.sell_mode=="grocery")].priority')
        mo_priority = jsonpath(result,
                               f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{la_info["warehouse_number"]}" && @.sell_mode=="mo")].priority')
        if grocery_priority[0] != 1:
            # TB1更新太慢，调用Item接口计算商品pantry属性
            wms.ec_item_api.pantry_transform([product_id], zipcode)
            # 最大重试次数
            max_retries = 24
            # 每次重试等待时间（秒）
            wait_time = 5
            for attempt in range(max_retries):
                # 检查库存平台商品库存
                result = wms.inv_db.query_item_region_mapping(product_id)
                grocery_priority = jsonpath(result,
                                            f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{houston_info["warehouse_number"]}" && @.sell_mode=="grocery")].priority')
                if grocery_priority[0] == 1:
                    break
                else:
                    print(f"Attempt {attempt + 1} failed. Retrying in {wait_time} seconds...")
                    # 等待一段时间后重试
                    wms.util.wait(sleep_time=wait_time)
            # wms.util.wait(sleep_time=wait_time)
            result = wms.inv_db.query_item_region_mapping(product_id)
            grocery_priority = jsonpath(result,
                                        f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{houston_info["warehouse_number"]}" && @.sell_mode=="grocery")].priority')
            mo_priority = jsonpath(result,
                                   f'$[?(@.region_id=="{region_id}" && @.warehouse_number=="{la_info["warehouse_number"]}" && @.sell_mode=="mo")].priority')

        print(grocery_priority)
        print(mo_priority)
        assert mo_priority[0] == 2, "商品pantry转自营售卖失败"
        assert grocery_priority[0] == 1, "商品pantry转自营售卖失败"

        # v5 接口校验
        wms.util.wait(sleep_time=6, reason='同步Redis延迟')
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, houston_info["warehouse_number"], adjust_qty)


if __name__ == "__main__":
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)
