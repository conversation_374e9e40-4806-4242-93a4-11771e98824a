import weeeTest
from weeeTest import jmespath
from datetime import datetime

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


def expired_checks(status, user_id, rec_id, item_number, location_no, warehouse_number, expired_dtm_input):
    """
    结果检查
    """
    sql_status = wms.wms_db.get_expired_check_task(rec_id)
    sql_s = sql_status[0]["status"]
    if sql_s == 1:
        weeeTest.log.info('一盘完成,待二盘......')
        # 更新盘点数据,继续二盘
        status = status + 1
        user_id = user_id + 'test1'
        edit_user = 'edit_user' + str(status)
        # 更新盘点user,继续下一盘
        wms.wms_db.update_expired_user(rec_id, user_id, edit_user)
        # 分配任务
        wms.expired_api.expired_assign_task(rec_id, user_id)
        weeeTest.log.info(f'进行{status + 1}盘, 更新{edit_user}:{user_id},count_id: {rec_id}')
    if sql_s == 2:
        weeeTest.log.info('二盘完成,待三盘......')
        # 更新盘点数据,继续三盘
        status = status + 1
        user_id = user_id + 'test2'
        edit_user = 'edit_user' + str(status)
        # 更新盘点user,继续下一盘
        wms.wms_db.update_expired_user(rec_id, user_id, edit_user)
        # 分配任务
        wms.expired_api.expired_assign_task(rec_id, user_id)
        weeeTest.log.info(f'进行{status + 1}盘, 更新{edit_user}:{user_id},count_id: {rec_id}')
    if sql_s == 4:
        weeeTest.log.info(f'count_id:{rec_id},盘点完成！！！')
        res_expire = wms.adjust_api.query_location_inv(item_number, location_no, warehouse_number)
        expire_dtm = jmespath(res_expire, "body.invAdjustList[0].expire_dtm")
        assert expired_dtm_input == expire_dtm, "库位保质期和盘点保质期不一致"

    return sql_s, status


class TestExpired(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_get_task(self):
        """
        【111839】保质期检查：领取保质期任务
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        # 参数设置
        warehouse_number = global_data.expiration_check['warehouse_number']
        storage_type = 1
        wms.expired_api.expired_get_task(warehouse_number, storage_type, edit_user)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_release_task(self):
        """
        【111840】保质期检查：释放保质期任务
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        wms.expired_api.expired_release_task(edit_user)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_bin_upc(self):
        """
        【111850】保质期检查：Bin库位UPC
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        # 数据
        warehouse_number = global_data.expiration_check['warehouse_number']
        location_no = 'E0504-1-4'
        item_number = global_data.expiration_check['item_number']
        # 查任务ID
        rec_ids = 128824
        wms.wms_db.update_expired_data(rec_ids, warehouse_number, location_no, item_number)
        res = wms.expired_api.expired_query_task(warehouse_number, location_no, item_number)
        rec_id = jmespath(res, "body.data[0].rec_id")
        status = int(jmespath(res, "body.data[0].status"))
        if rec_ids == rec_id:
            wms.expired_api.expired_assign_task(rec_id, user_id)
            sql_s = status  # 根据盘点状态进行盘点
            # 盘点次数循环：1/2/3
            while sql_s < 3:
                res1 = wms.expired_api.expired_scan_location(warehouse_number, location_no, status, edit_user)
                upc = jmespath(res1, "body[0].upc_code")
                wms.expired_api.expired_goods(warehouse_number, upc, location_no)
                # 获取当前时间expired_date加一年
                date = datetime.now()
                expired_dtm_input = date.replace(year=date.year + 1).strftime('%Y-%m-%d')
                expired_dtm_str = date.replace(year=date.year + 1, hour=0, minute=0, second=0, microsecond=0).strftime('%Y/%m/%d %H:%M:%S')
                wms.expired_api.expired_confirm(warehouse_number, location_no, item_number, edit_user, status, expired_dtm_str, expired_dtm_input)
                # 查询任务状态
                sql_s, status = expired_checks(status, user_id, rec_id, item_number, location_no, warehouse_number, expired_dtm_input)
        # 退出登录
        wms.wms_login.logout(warehouse_number, user_id, user_name)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_stock_lpn(self):
        """
        【111851】保质期检查：Stock库位LPN
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        # 数据
        warehouse_number = global_data.expiration_check['warehouse_number']
        location_no = 'F1028-2-3'
        item_number = global_data.expiration_check['item_number']
        # 查任务ID
        rec_ids = 133038
        wms.wms_db.update_expired_data(rec_ids, warehouse_number, location_no, item_number)
        res = wms.expired_api.expired_query_task(warehouse_number, location_no, item_number)
        rec_id = jmespath(res, "body.data[0].rec_id")
        status = int(jmespath(res, "body.data[0].status"))
        if rec_ids == rec_id:
            wms.expired_api.expired_assign_task(rec_id, user_id)
            sql_s = status  # 根据盘点状态进行盘点
            # 盘点次数循环：1/2/3
            while sql_s < 3:
                res1 = wms.expired_api.expired_scan_location(warehouse_number, location_no, status, edit_user)
                upc = jmespath(res1, "body[0].upc_code")
                res2 = wms.expired_api.expired_goods(warehouse_number, upc, location_no)
                lpn_lists = jmespath(res2, "body.lpnList")
                # 获取当前时间expired_date加一年
                date = datetime.now()
                expired_dtm_input = date.replace(year=date.year + 1).strftime('%Y-%m-%d')
                expired_dtm_str = date.replace(year=date.year + 1, hour=0, minute=0, second=0, microsecond=0).strftime('%Y/%m/%d %H:%M:%S')
                wms.expired_api.expired_lpn_confirm(warehouse_number, location_no, item_number, edit_user, status, expired_dtm_str, expired_dtm_input,
                                                    lpn_lists)
                # 查询任务状态
                sql_s, status = expired_checks(status, user_id, rec_id, item_number, location_no, warehouse_number, expired_dtm_input)
        # 退出登录
        wms.wms_login.logout(warehouse_number, user_id, user_name)
