# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business.picking_process import general_picking_operate
from wms.test_dir.business.down_order_process import DownOrderProcess




class TestOrderOutboundProcess(weeeTest.TestCase):
    """
    订单下单-down单-预占-Picking-Packing-Route Check全流程Case
    """

    def setup_class(self):
        self.warehouse_number = "48"
        self.storage_type = "2"
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.current_date = wms.util.get_special_date(days=1)
        self.order_type_list = global_data.outbound_info["order_type"]
        self.delivery_date = global_data.outbound_info["delivery_date"]

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_manual_down_order(self):
    #     """
    #     normal down order流程
    #     """

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_ready_for_picking(self):
    #     """
    #     ready_for_picking流程
    #     """
        

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_generate_batch(self):
    #     """
    #     generate_batch流程
    #     """
    #     for order_type in self.order_type_list:
    #         generate_batch_operate(self.warehouse_number, self.delivery_date, order_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_picking(self):
        """
        【112950】Normal picking 流程
        """

        warehouse_number = global_data.normal_picking['warehouse_number']
        storage_type = global_data.normal_picking['storage_type']

        general_picking_operate(warehouse_number, storage_type)


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mailorder_picking(self):
        """
        【112953】Mail Order picking 流程
        """
        warehouse_number = global_data.mo_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=50, picking_channel=1, module_name="mail_order")


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_oneitem_picking(self):
        """
        【112952】Oneitem picking 流程
        """
        warehouse_number = global_data.one_item_picking['warehouse_number']

        general_picking_operate(warehouse_number, "1", location_type=52, is_oneitem=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bulk_picking(self):
        """
        【112951】Bulk picking 流程
        """
        warehouse_number = global_data.bulk_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=73, is_bulk=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mof_picking(self):
        """
        【112954】MOF picking 流程
        """
        warehouse_number = global_data.mof_picking['warehouse_number']
        general_picking_operate(warehouse_number, "6", location_type=50, picking_channel=1, module_name="mail_order", is_mof=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_fbw_picking(self):
        """
        【112955】Fbw picking 流程
        """
        warehouse_number = global_data.fbw_picking['warehouse_number']
        storage_type = global_data.fbw_picking['storage_type']
        general_picking_operate(warehouse_number, storage_type, location_type=75)


    def teardown_class(self):
        # 退出登录
        wms.logout(warehouse_number=self.warehouse_number, user_id=self.user_id, user_name=self.user_name)
        


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)