# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_auto_pack.py
@Description    :
@CreateTime     :  2023/11/23 17:58
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/23 17:58
"""
import platform

import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.fpo.fpo import fpo
from tms.test_dir.api.tms.tms import tms
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business.picking_process import general_picking_operate
from wms.test_dir.api.wms.wms_autopicking import wms_auto


check_window = True if platform.system().lower() == 'windows' else False

"""
down_order, generate_delivery, init_hot_package, batch, picking, packing, route_check
执行参考文档: https://docs.google.com/document/d/1x4ciV1Ooc8Lz-V6Ce06Eve2K7QOnGdp4J2u9kuIMqcQ
"""

so_order_id = 42648771  # 订单ID  42603831  42604102
pack_all = False  # 是否打包所有订单
dispatch = 863296  # 打包指定派车单,前提 pack_all为 False, 不指定时为None
need_route = False  # 是否需要排车, True:需要排车, False:不排车

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'route_check', "--junitxml=tests/results.xml"])


class TestAutoPack(weeeTest.TestCase):
    """
    数据构造
    """

    @weeeTest.mark.skipif(not check_window, reason='数据构造脚本,不用定期执行')
    @weeeTest.mark.list('tools', 'down_order')
    def test_process_down_order(self):
        """
        数据构造->截贴生成批次(数据构造脚本,不用定期执行)
        """
        # 账户登录
        tms.login()
        wms.login()
        wms.down_order_process(order_id=so_order_id)

    @weeeTest.mark.skipif(not check_window, reason='数据构造脚本,不用定期执行')
    @weeeTest.mark.list('tools', 'generate_delivery')
    def test_process_generate_delivery(self):
        """
        数据构造->排车下发WMS(数据构造脚本,不用定期执行)
        """
        tms.login()
        wms.login()
        config_id = tms.tms_db.get_fpo_order_config_id(order_id=so_order_id)
        warehouse_number = tms.tms_db.get_processing_warehouse_number(config_id=config_id)
        delivery_id = tms.tms_db.get_delivery_id_by_order_id(order_id=so_order_id)
        delivery_type = tms.tms_db.get_delivery_type_by_delivery_id(delivery_id=delivery_id)
        # 生成派车计划
        delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_id)
        if need_route:
            # 修改系统拣货配置
            wms.update_wh_config_picking_check(warehouse_number=warehouse_number, picking_check='true')
            # 算法排车,根据派车计划生成排车草稿-(TMS)
            if delivery_type == 'delivery':
                draft_res = tms.central.route_plan.run_v14(delivery_plan_id=delivery_plan_id)
                draft_id = tms.util.get_response_values(draft_res, 'draft_id[0]')[0]
                # 等待算法执行完成
                tms.wait_algorithm_finish(draft_id)
                # 应用排车草稿,下发WMS
                tms.create_dispatch_check(draft_id)
            else:
                draft_res = tms.central.route_plan.v12_run_draft(delivery_plan_id=delivery_plan_id)
                draft_id = tms.util.get_response_values(draft_res, 'draft_id[0]')[0]
                # 等待算法执行完成
                tms.wait_algorithm_finish(draft_id)
                # 应用排车草稿,下发WMS
                tms.create_dispatch_check(draft_id)
                # 初始化热送包裹
                tms.central.route_plan.hot_dish_initialize(delivery_id=delivery_id)


    @weeeTest.mark.skipif(not check_window, reason='数据构造脚本,不用定期执行')
    @weeeTest.mark.list('tools', "batch")
    def test_process_batch(self):
        """
        数据构造->预占(数据构造脚本,不用定期执行)
        """
        tms.login()
        wms.login()
        # # 获取帖子信息
        deal_id = tms.tms_db.get_deal_id_by_order(order_id=so_order_id)
        # 获取配送日期
        delivery_date = tms.tms_db.get_deal_delivery_date(deal_id=deal_id)
        if pack_all:
            #  获取帖子下面的所有订单(针对不需要排车的订单类型,如MO,MOF)
            so_order_ids = tms.tms_db.get_deal_order_ids(deal_id=deal_id)
        elif dispatch:
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch)
        else:
            # 获取对应派车单ID
            dispatch_id = tms.tms_db.get_dispatch_info_by_order_id(order_id=so_order_id)
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch_id[0])

        # 添加库存
        sku_info = wms.add_sku(so_order_ids=so_order_ids)
        warehouse_numbers = tuple(set(info['warehouse_number'] for info in sku_info))

        #FBW订单清场
        result = wms.wms_db.get_so_order_mapping(order_id=so_order_ids, info=['wot.tag_id'])
        if result is None:
            print("No result found for the given so_order_ids")
        else:
            if not isinstance(result, list):
                result = [result]

            # 查询tag_id = 4
            has_tag_id_4 = any(item.get('tag_id') == 4 for item in result if isinstance(item, dict))
            if has_tag_id_4:
                #清场
                before_delivery_dtm = wms.change_sys_date(warehouse_numbers=warehouse_numbers, delivery_date=delivery_date-86400)
                wms.wms_db.update_order(order_status=2, delivery_dtm=before_delivery_dtm, warehouse_number=warehouse_numbers)
                wms.fbw_picking.fbw_clear()
            else:
                # 如果有非空的 tag_id，但都不等于 4
                print("Found non-empty tag_ids, but none are equal to 4")
                # 其他逻辑

        # 修改系统日期
        wms.change_sys_date(warehouse_numbers=warehouse_numbers, delivery_date=delivery_date)

        # 预占-(WMS)
        order_info = tuple(set(tuple((info['warehouse_number'], info['order_type']) for info in sku_info)))
        for info in order_info:
            wms.batch.pre_occupancy_and_batch(warehouse_number=info[0], order_type=info[1])
        # 查询是否预占成功
        wms_order_ids = tuple(set(info['order_id'] for info in sku_info))
        for wms_order_id in wms_order_ids:
            # 手动触发预占
            wms_order_info = wms.wms_db.get_wms_order_info(wms_order=wms_order_id)
            wms.batch.manual_orders_batch(order_id=wms_order_id, delivery_date=delivery_date,
                                          order_type=wms_order_info['order_type'],
                                          warehouse_number=wms_order_info['warehouse_number'])
        wms.wms_db.check_orders_shipping_status(order_ids=wms_order_ids, shipping_status=20)

    @weeeTest.mark.skipif(not check_window, reason='数据构造脚本,不用定期执行')
    @weeeTest.mark.list('tools', "picking")
    def test_process_picking(self):
        """
        数据构造->拣货(数据构造脚本,不用定期执行)
        """
        tms.login()
        wms.login()
        # 获取派车单下所有SO订单
        deal_id = tms.tms_db.get_deal_id_by_order(order_id=so_order_id)
        if pack_all:
            #  获取帖子下面的所有订单(针对不需要排车的订单类型,如MO,MOF)
            so_order_ids = tms.tms_db.get_deal_order_ids(deal_id=deal_id)
        elif dispatch:
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch)
        else:
            # 获取对应派车单ID
            dispatch_id = tms.tms_db.get_dispatch_info_by_order_id(order_id=so_order_id)
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch_id[0])
        # 获取
        wms_order_ids = wms.wms_db.get_wms_order_ids(so_order_ids, shipping_status=20)
        wms_order_list = tuple(item['order_id'] for item in wms_order_ids)
        batch_ids = wms.wms_db.get_batch_ids(wms_orders=wms_order_list)
        # 设置batch优先级最高
        wms.wms_db.update_batch_priority(batch_ids=batch_ids)
        sku_info = wms.wms_db.get_wms_order_by_order_ids(order_ids=so_order_ids)
        order_info = tuple(set(tuple((info['warehouse_number'], info['storage_type'], info['item_number'], info['order_type'],info['order_id'],info['shipping_status'],info['tag_id']) for info in sku_info)))
        #  处理当前账户未完成的拣货任务
        wms.wms_db.finish_picking_task()
        for item in order_info:
            if item[5] == 25 and item[0] == 25:
                wms_auto.as_picking(order_id=item[4])
            elif item[5] == 25 and item[0] == 41:
                wms_auto.geek_picking(order_id=item[4])
            elif item[3] == 4:
                general_picking_operate(warehouse_number=item[0], storage_type="1", location_type=50, picking_channel=1, module_name="mail_order")
            elif item[3] == 6:
                general_picking_operate(warehouse_number=item[0],storage_type= "6", location_type=50, picking_channel=1,module_name="mail_order", is_mof=True)
            elif item[6] == 3:
                general_picking_operate(warehouse_number=item[0],storage_type= "1", location_type=73, is_bulk=True)
            #fbw
            elif item[6] ==4:
                general_picking_operate(warehouse_number=item[0], storage_type=item[3], location_type=75)

            else:
                general_picking_operate(warehouse_number=item[0], storage_type=item[3])

    @weeeTest.mark.skipif(not check_window, reason='数据构造脚本,不用定期执行')
    @weeeTest.mark.list('tools', "packing")
    def test_process_packing(self):
        """
        数据构造->打包(数据构造脚本,不用定期执行)
        """
        tms.login()
        wms.login()
        deal_id = tms.tms_db.get_deal_id_by_order(order_id=so_order_id)
        if pack_all:
            #  获取帖子下面的所有订单(针对不需要排车的订单类型,如MO,MOF)
            so_order_ids = tms.tms_db.get_deal_order_ids(deal_id=deal_id)
        elif dispatch:
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch)
        else:
            # 获取对应派车单ID
            dispatch_id = tms.tms_db.get_dispatch_info_by_order_id(order_id=so_order_id)
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch_id[0])
        # 获取订单库存SKU
        sku_info = wms.wms_db.get_info_by_order_ids(order_ids=so_order_ids)
        finish_order_id = []
        for item in sku_info:
            if item['order_id'] not in finish_order_id:
                finish_order_id.append(item['order_id'])
                # 打包-(WMS)
                wms.auto_normal_packing_operation(warehouse=item['warehouse_number'], order_id=item['order_id'],
                                                  tote_no=item['tote_no'], storage_type=item['storage_type'])

    @weeeTest.mark.skipif(not check_window, reason='数据构造脚本,不用定期执行')
    @weeeTest.mark.list('tools', "route_check")
    def test_process_route_check(self):
        """
        数据构造->路线检查(数据构造脚本,不用定期执行)
        """
        deal_id = tms.tms_db.get_deal_id_by_order(order_id=so_order_id)
        if pack_all:
            #  获取帖子下面的所有订单(针对不需要排车的订单类型,如MO,MOF)
            so_order_ids = tms.tms_db.get_deal_order_ids(deal_id=deal_id)
        elif dispatch:
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch)
        else:
            # 获取对应派车单ID
            dispatch_id = tms.tms_db.get_dispatch_info_by_order_id(order_id=so_order_id)
            # 获取派车单下所有的Order_ids
            so_order_ids = tms.tms_db.get_dispatch_order_ids(dispatch_id=dispatch_id[0])
        # 获取wms_order_ids
        wms_order_ids = wms.wms_db.get_wms_order_ids(so_order_ids)
        wms_order_list = tuple(item['order_id'] for item in wms_order_ids)
        wms.wms_db.set_route_check_status(wms_orders=wms_order_list)

    @weeeTest.mark.skipif(not check_window, reason='数据构造脚本,不用定期执行')
    @weeeTest.mark.list('tools', "delivery_dispatch")
    def test_process_delivery_dispatch(self):
        """
        数据构造->配送(数据构造脚本,不用定期执行)
        """
        tms.login()
        wms.login()
        dispatch_id = tms.tms_db.get_dispatch_info_by_order_id(order_id=so_order_id)[0]
        # dispatch_id = 490779
        delivery_type = tms.tms_db.get_delivery_type_by_dispatch_id(dispatch_id=dispatch_id)
        # 更新派车单日期
        tms.tms_db.update_dispatch_status(dispatch_id=dispatch_id, delivery_date=tms.util.us_current_date(),
                                          status='')
        # 更新派车单司机
        tms.central.driver_manage.update_drivers(dispatch_id=dispatch_id, driver_id=global_data.driver_user_id,
                                                 driver_name=global_data.driver_user_name)
        if delivery_type == 'hot_delivery':
            tms.hot_delivery_dispatch(dispatch_id=dispatch_id)
        else:
            tms.delivery_dispatch(dispatch_id=dispatch_id)
