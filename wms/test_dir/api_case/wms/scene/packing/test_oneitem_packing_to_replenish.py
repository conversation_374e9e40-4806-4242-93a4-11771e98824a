# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestOneItemPackingToReplenish(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_oneitem_packing_to_replenish(self):
        """
        【112830】oneitem打包转补单流程
        """
        # 登录
        account, username = wms.wms_login.common_login()

        warehouse = global_data.one_item_packing_to_replenish['warehouse']
        cart_no = global_data.one_item_packing_to_replenish['cart_no']
        order_id = global_data.one_item_packing_to_replenish['order_id']
        storage_type = global_data.one_item_packing_to_replenish['storage_type']

        warehouse_time = wms.get_central_config(warehouse, "wms:order:delivery_date")
        print(type(warehouse_time))
        print(warehouse_time)
        if warehouse_time is None:
            warehouse_time = wms.util.utc_current_day()
        else:
            warehouse_time = wms.util.day_to_utc(warehouse_time)
        wms.wms_db.rollback_wait_packing(warehouse, order_id, cart_no, warehouse_time)

        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))

        # 查询cart 下待打包的订单
        cart_data = wms.wms_db.select_cart_orders(cart_no=cart_no, warehouse=warehouse)
        if isinstance(cart_data, dict):
            cart_data = [cart_data]

        # 开始打包
        wms.normal_packing.user = username + '(' + account + ')'
        wms.normal_packing.warehouse = warehouse

        # check batch
        is_batch = wms.get_inventory_batch_switch(warehouse, storage_type)

        resp = wms.normal_packing.query_packing_task(cart_no)
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))

        ret = wms.normal_packing.query_packing_info(cart_no)
        waiting_packing_order_num = ret["waitingPackingOrderNum"]
        weeeTest.log.info(f'打包台查出{cart_no}中待打包订单数量:{waiting_packing_order_num}')

        # 查询可用的tote转补单
        tote_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=6, flag=0,
                                                          info='location_no')
        for order in cart_data:
            items, order_id, recommend_package = wms.normal_packing.one_item_get_order(cart_no, order["upc"])
            # 当打包Tote/cart中库存数据异常时，恢复库存
            if wms.adjust_api.query_location_inv_list(warehouse, cart_no) == []:
                for item in items:
                    if isinstance(item, dict):
                        wms.adjust_api.create_batch_inv(item["item_number"], warehouse, username + '(' + account + ')',
                                                        cart_no, item["item_quantity"])
                    if is_batch:
                        item["batch_no"] = wms.wms_db.get_batch_inventory_transaction(warehouse, cart_no, item["item_number"])["batch_no"]

            oos_item = wms.normal_packing.oneitem_oos_qc(items, order_id)
            wms.normal_packing.query_replenish_quantity(cart_no, order_id)
            wms.normal_packing.oneitem_packing_replenish(tote_no, cart_no, order_id, oos_item)
        wms.normal_packing.query_packing_task(cart_no)
        wms.normal_packing.query_packing_info(cart_no)
        if not wms.wms_db.select_cart_orders(cart_no=cart_no, warehouse=warehouse):
            wms.normal_packing.one_item_complete(cart_no)

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=42)

        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=cart_no, status=0)
        # check cart inventory empty
        wms.wms_assert.check_location_empty(warehouse, cart_no)

        # 补单流程
        # replenish order check in
        wms.replenish.check_tote(tote_no)
        slot_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=35, flag=0,
                                                          info='location_no')
        wms.replenish.check_slot(slot_no, order_id)
        wms.replenish.bind_slot(tote_no, slot_no)
        wms.wms_db.check_shipping_status(order_id=order_id, status=45)
        # replenish force complete
        wms.replenish.checkout_force_complete(tote_no, slot_no)

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=51)

        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=slot_no, status=0)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)
        # 将order的状态置为结束，避免被其他业务使用
        wms.wms_db.update_order_ship_status(ship_status=70, order_id=order_id)
        wms.wms_db.update_location_status(warehouse, cart_no, 3)
        # 还原oneitem 补单数据
        wms.wms_db.update_pick_order(order_id=order_id)
        wms.wms_db.update_pick_order_item(order_id=order_id)
        wms.wms_db.update_location_status(warehouse, tote_no, 0)

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)