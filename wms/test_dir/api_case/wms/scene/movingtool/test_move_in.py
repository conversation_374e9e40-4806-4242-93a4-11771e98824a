import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api.wms.movingtool.moving_api import Moving


class TestMoveOut(weeeTest.TestCase):
    # UPC转移
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_move_in_upc_1(self):
        """
        【111586】MoveIn：【UPC】E0001转移到Stock
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = global_data.moving_tool['warehouse_number']
        location_from = "E0001"
        location_to = "F0318-1-6"
        upc_code = "2000000096627"
        qty = 1
        item_number = global_data.moving_tool['item_number']
        move_in = True
        batch_no = "240626003233"
        lpn_no = None
        location_no = location_to
        # 调用item查询接口
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        # 转移前查询E0001库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 调用转移接口
        Moving().move_upc(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user)
        # 转移后查询E0001库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整后库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_move_in_upc_2(self):
        """
        【111587】MoveIn：【UPC】E0001转移到D-Stock
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = global_data.moving_tool['warehouse_number']
        location_from = "E0001"
        location_to = "D-Stock"
        upc_code = "2000000096627"
        qty = 1
        item_number = global_data.moving_tool['item_number']
        move_in = True
        batch_no = None
        lpn_no = None
        location_no = location_to
        # 调用item查询接口
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        # 转移前查询E0001库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 调用转移接口
        Moving().move_upc(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user)
        # 转移后查询E0001库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整后库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

    # LPN转移
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_move_in_lpn_1(self):
        """
        【112899】MoveIn：【LPN】E0006转移到Stock
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = global_data.moving_tool['warehouse_number']
        location_from = "E0006"
        location_to = "F1003-1-4"
        item_number = global_data.moving_tool['item_number']
        move_in = True
        batch_no = "250603000683"
        location_no = location_to
        # 调用item查询接口获取LPN信息
        resp1 = Moving().query_item_qty(item_number=item_number, location_to=location_from, warehouse_number=warehouse_number)
        lpn_list = resp1[1]  # 获取lpn_list
        lpn_no = lpn_list[0]["lpnNo"]
        upc_code = lpn_no
        # 遍历lpn_list数据，找到匹配的lpnNo并提取对应的quantity值
        qty = None
        for entry in lpn_list:
            if entry["lpnNo"] == lpn_no:
                qty = entry["quantity"]
                break
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        lpn_info_lists = jmespath(resp, "body.lpn_info_list")  # 获取lpn_info_list
        # 遍历数据，找到符合lpn_no条件的字典
        lpn_info_list = next((item for item in lpn_info_lists if any(lpn["lpn_no"] == lpn_no for lpn in item["lpn_no_list"])))
        # 转移前查询E0006库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 调用转移接口
        Moving().move_lpn(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, receive_dtm, lpn_info_list, in_user)
        # 转移后查询E0006库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整后库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

# SKU：96627
