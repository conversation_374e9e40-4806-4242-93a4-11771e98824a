import weeeTest
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.simple.cycle_count.simple_count_api import CountApi


def count_stock_lpn_common(warehouse_number, edit_user, count_id, in_user, user_id):
    """
	Stock_upc盘点通用方法
	"""
    # 参数配置
    storage_type = "2"
    location_type = 3
    status = 0
    module_name = "stock_cycle_count"
    weeeTest.log.info('盘点开始............................')
    # 查看领取的任务
    count_data = CountApi().get_count_data(warehouse_number, edit_user, storage_type, location_type, status, module_name)
    assert count_id == count_data[0], "领取的任务ID和盘点ID不一致"  # 校验任务ID是不是和参数一致
    count_id = count_data[0]
    location_no = count_data[1]
    status = count_data[2]
    sql_s = status  # 根据盘点状态进行盘点
    # 盘点次数循环：1/2/3
    while sql_s < 3:
        detail = CountApi().get_location_detail(count_id, status, module_name)
        sku_list = detail[0]
        resp1 = detail[1]
        # SKU个数获取
        if sku_list > 0:
            sku_data = CountApi().get_sku_detail(resp1, location_no, warehouse_number)
            item_list = sku_data[0]
            is_lpn_list = sku_data[1]
            for i in range(len(item_list)):
                item_number = item_list[i]
                is_lpn = is_lpn_list[i]
                # 判断是LPN盘点还是UPC盘点
                if is_lpn is None:
                    pass  # 走UPC盘点
                else:
                    # LPN盘点
                    CountApi().count_stock_lpn(is_lpn, warehouse_number, location_no, item_number, in_user, status, module_name)
            # 盘点finish
            CountApi().count_finish(count_id, in_user, sku_list, module_name, status)
            # 盘点结果检查
            sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)
        else:
            # 没有SKU,走empty流程
            CountApi().count_empty(count_id, in_user, status, module_name)
            # 盘点结果检查
            sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)


class TestCountStockLpn(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_lpn_count_qty(self):
        """
		【111600】Stock_LPN盘点:数量盘点
		"""

        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 初始化盘点数据
        warehouse_number = "48"
        count_id = 12558288  # 盘点ID,D2106-2-1
        location_no = "D2106-2-1"
        CountApi().release_task(edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(warehouse_number, count_id, user_id)  # 分配任务
        count_stock_lpn_common(warehouse_number, edit_user, count_id, in_user, user_id)  # 调用盘点通用方法
        # 退出登录
        wms.wms_login.logout(warehouse_number, user_id, user_name)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_lpn_count_empty(self):
        """
		【111602】Stock_LPN盘点:empty流程
		"""

        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 初始化盘点数据
        warehouse_number = "48"
        count_id = 12558289  # 盘点ID,B0105-2-3
        location_no = "D0101-1-3"
        CountApi().release_task(edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(warehouse_number, count_id, user_id)  # 分配任务

        # 参数获取
        storage_type = "2"
        location_type = 3
        status = 0
        module_name = "stock_cycle_count"
        weeeTest.log.info('盘点开始............................')
        # 查看领取的任务
        count_data = CountApi().get_count_data(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        assert count_id == count_data[0], "领取的任务ID和盘点ID不一致"  # 校验任务ID是不是和参数一致
        count_id = count_data[0]
        status = count_data[2]
        sql_s = status  # 根据盘点状态进行盘点
        # 盘点次数循环：1/2/3
        while sql_s < 3:
            CountApi().get_location_detail(count_id, status, module_name)
            # 走empty流程
            CountApi().count_empty(count_id, in_user, status, module_name)
            # 盘点结果检查
            sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)
        # 退出登录
        wms.wms_login.logout(warehouse_number, user_id, user_name)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_lpn_count_lpn_missing(self):
        """
		【111596】Stock_LPN盘点:LPN Missing流程
		"""
        pass
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 初始化盘点数据
        warehouse_number = "48"
        count_id = 12558290  # 盘点ID,B1005-1-2
        location_no = "D0102-1-2"
        CountApi().release_task(edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(warehouse_number, count_id, user_id)  # 分配任务

        # 参数配置
        storage_type = "2"
        location_type = 3
        status = 0
        module_name = "stock_cycle_count"
        weeeTest.log.info('盘点开始............................')
        # 查看领取的任务
        count_data = CountApi().get_count_data(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        assert count_id == count_data[0], "领取的任务ID和盘点ID不一致"  # 校验任务ID是不是和参数一致
        count_id = count_data[0]
        location_no = count_data[1]
        status = count_data[2]
        sql_s = status  # 根据盘点状态进行盘点
        # 盘点次数循环：1/2/3
        while sql_s < 3:
            detail = CountApi().get_location_detail(count_id, status, module_name)
            sku_list = detail[0]
            resp1 = detail[1]
            # SKU个数获取
            if sku_list > 0:
                sku_data = CountApi().get_sku_detail(resp1, location_no, warehouse_number)
                item_list = sku_data[0]
                is_lpn_list = sku_data[1]
                for i in range(len(item_list)):
                    item_number = item_list[i]

                    is_lpn = is_lpn_list[i]
                    # 判断是LPN盘点还是UPC盘点
                    if is_lpn is None:
                        pass  # 走UPC盘点
                    else:
                        # LPN盘点
                        lpn_list = []  # 拼接confirm的LPN list
                        for lpn in is_lpn:
                            new_lpn_list = {
                                "lpn_no": lpn["lpn_no"],
                                "expired_dtm_str": None,
                                "pieces_per_pack": lpn["pieces_per_pack"],
                                "inputQty": lpn["lpn_quantity"],
                                "warehouse_number": lpn["warehouse_number"],
                                "item_number": lpn["item_number"],
                                "location_no": lpn["location_no"],
                                "lot_code": lpn["lot_code"],
                                "date_type": lpn["date_type"]
                            }
                            lpn_list.append(new_lpn_list)
                        missing_lpn = {
                            "warehouse_number": "29",
                            "item_number": "35256",
                            "location_no": "B1005-1-2",
                            "lpn_no": "",
                            "expired_dtm_str": "2026-01-01",
                            "inputQty": 25,
                            "pieces_per_pack": "25",
                            "lot_code": None,
                            "date_type": 0
                        }
                        lpn_list.append(missing_lpn)
                        # 调用confirm接口
                        wms.count.lpn_confirm(warehouse_number, location_no, item_number, in_user, status, module_name, lpn_list)
                # 盘点finish
                CountApi().count_finish(count_id, in_user, sku_list, module_name, status)
                # 盘点结果检查
                sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)
            else:
                # 没有SKU,走empty流程
                CountApi().count_empty(count_id, in_user, status, module_name)
                # 盘点结果检查
                sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)

        # 退出登录
        wms.wms_login.logout(warehouse_number, user_id, user_name)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
