import time
import weeeTest
from wms.test_dir.api.wms.wms import header
import json


class CentralInventory(weeeTest.TestCase):
    """
    central inv 相关接口
    """

    def update_product_inv_status(self):
        """
        商品售卖状态刷新(计划售卖)
        """
        self.get(url='/central/inventory/job/update/product/inv/status', headers=header)
        assert self.response['result'] is True

    def update_overtime_payment_inv(self):
        """
        取消订单异常商品库存修复
        """
        self.get(url='/central/inventory/job/update/overtime/payment/inv/detail', headers=header)
        assert self.response['result'] is True

    def job_save_daily_inv_snapshot(self):
        """
        库存备份，仓库商品某一时刻库存快照
        """
        self.get(url='/central/inventory/job/save/daily/inventory/snapshot', headers=header)
        assert self.response['result'] is True

    def redis_detail_query(self, item_number):
        """
        redis库存查询
        """
        url = "/central/inventory/admin/redis/detail/query"
        data = json.dumps(item_number)
        self.post(url=url, headers=header, data=data)
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def inv_redis_key_del(self, key_list):
        """
        redis库存删除
        """
        if not key_list or not isinstance(key_list, list):
            return
        url = "/central/inventory/admin/redis/run/del"
        data = json.dumps(key_list)
        self.post(url=url, headers=header, data=data)
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def fulfill_region_query(self, data):
        """
        Redis履约信息查询接口
        """
        url = "/central/inventory/admin/redis/run/get"
        body = [data]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def priority_product(self, product_id):
        """
        更新商品售卖优先级
        """
        url = "/central/inventory/sync/sell/priority/product/id"
        body = [product_id]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_local_inv(self, productIds, dates):
        """
        分页查询商品对应日期的售卖库存
        """
        url = "/central/inventory/query/product/local/inv"
        data = {
            "dates": dates,
            "page": 0,
            "pageSize": 100,
            "productIds": productIds,
            "return_sales_org": True,
            "startColumn": 0
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_region_inv(self, sales_org_id, date, zipcode, product_id, deliveryQty=False, channelType="main"):
        """
        查询商品每日售卖库存
        deliveryQty: 是否查询履约库存
        """
        url = "/central/inventory/region/query"
        data = {
            "date": date,
            "zipcode": zipcode,
            "product_id": product_id,
            "deliveryQty": deliveryQty,
            "channelType": channelType
        }
        if sales_org_id:
            data["sales_org_id"] = sales_org_id
        self.post(url=url, headers=header, data=json.dumps([data]))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_sales_inv(self, sales_org_id, date, product_ids):
        """
        批量查询Product库存(替换V2接口)
        product_ids: 商品List,eg:[30702]
        """
        url = "/central/inventory/query/local/warehouse/sales/inv"
        data = {
            "sales_org_id": sales_org_id,
            "date": date,
            "product_ids": product_ids
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_all_date_inv_v4(self, product_id, product_type="normal"):
        """
        查询商品的销售组织可售日期库存v4
        """
        url = "/central/inventory/query/all/date/v4"
        data = [
            {
                "product_id": product_id,
                "product_type": product_type
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_org_inv(self, sales_model, sales_org_id):
        """
        查询销售组织下有（非pantry库存）库存的商品ID
        """
        url = "/central/inventory/query/sales_org_id/inv"
        data = {
            "sales_model": sales_model,
            "sales_org_id": sales_org_id
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_all_region_inv(self, product_ids):
        """
        查询商品所有region下的可售库存
        """
        url = "/central/inventory/query/all/region/inv"
        data = product_ids
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_warehouse_inv(self, warehouse_number, sales_model, product_id=None):
        """
        查询仓库商品总库存
        """
        url = "/central/inventory/query/inventory/product/inv"
        data = {
            "warehouse_number": warehouse_number,  # 必填,真实仓库号
            "page_no": 1,  # 必填,page_no > 0
            "page_size": 1000,  # 必填,page_size > 0 & page_size <= 1000
            "sales_model": sales_model,  # 非必填
            "productIds": [product_id]  # 非必填,不填查询仓库有库存的商品
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_local_warehouse_inv(self, productIds, warehouseNumber=None, salesOrgId=None):
        """
        查询商品本地仓库的库存信息
        """
        url = "/central/inventory/query/local/warehouse/inv"
        data = {
            "productIds": productIds,
            "salesOrgId": salesOrgId,  # productIds（不能超过1000个）、 salesOrgId 、warehouseNumber  三个参数至少有一个有值
            "pageSize": 500,
            "startColumn": 0,
            "orders": [
                {
                    "orderColumn": "available_qty",
                    "orderRule": "desc"
                },
                {
                    "orderColumn": "reserved_qty",
                    "orderRule": "desc"
                }
            ]}
        if warehouseNumber:
            data["warehouseNumber"] = warehouseNumber
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_seller_inv(self, product_id, seller_id):
        """
        查询seller商品库存
        """
        url = "/central/inventory/query/seller/product/inv"
        data = [
            {
                "product_id": product_id,
                "reference_no": seller_id  # 商家ID查Global+的seller,不查FBW;
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_global_fbw_inv_detail(self, warehouse_number, product_id):
        """
        Global Fbw库存信息查询接口
        """
        url = "/central/inventory/query/product/inventory/detail"
        data = {
            "warehouse_numbers": [warehouse_number],
            "productIds": [product_id],
            "is_all_product": "false",
            "biz_type": "mkpl_fbw",
            "pageSize": 500,
            "startColumn": 0,
            "order": {
                "orderColumn": "available_qty,order_qty",
                "orderRule": "DESC"
            }
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_fbw_inv_summary(self, seller_id, region_id, date):
        """
        FBW 库存销量查询接口
        """
        url = "/central/inventory/inventory_summary"
        data = {
            "page": 1,
            "size": 100,
            "seller_id": seller_id,
            "region_id": region_id,
            "product_ids": [],  # product_ids非必传
            "dates": [date]
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_seller_warehouse_product(self, seller_id, warehouse_number):
        """
        查询Global FBW的商家在仓库的商品ID
        """
        url = "/central/inventory/query/seller/warehouse/productIds"
        data = {
            "seller_ids": [seller_id],  # 必填
            "warehouse_numbers": [warehouse_number]  # 必填
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_warehouse_inv_detail(self):
        """
        merch的Inventory Information Detail-Inventory Batch调用
        """
        url = "/central/inventory/query/product/warehouse/inv/detail"
        data = {
            "productBizTypes": [
                "normal",
                "fbw"
            ],
            "warehouseNumbers": [
                "29"
            ],
            "productIds": [
                "56852 "
            ],
            "pageSize": 499,
            "startColumn": 0,
            "sellable_condition": -1,
            "isReturnBatchInv": True
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_sales_mapping(self):
        """
        查询商品在销售组织下对应的仓库mapping关系
        """
        url = "/central/inventory/query/product/sales/mapping"
        data = {
            "productIds": [
                50523
            ],
            "sales_org_id": 9
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_warehouse_sales_mapping(self):
        """
        查询销售组织+仓库+workType+实物仓库关系
        """
        url = "/central/inventory/query/warehouse/sales"
        data = {
            "work_type": "dry",
            "sales_org_id": 3,
            "warehouse_number": "25"  # 三个参数 至少有一个有值
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def reserve_start(self, start_date, end_date, product_id, reserve_qty, sales_org_id):
        """
        创建赠品活动预占库存
        """
        url = "/central/inventory/reserve/start"
        trace_id = "autotest_reserve_" + str(int(round(time.time() * 1000)))
        data = [
            {
                "comment": "autotest......",
                "end_date": end_date,
                "isMo": 0,
                "product_id": product_id,
                "real_reserve_qty": 0,
                "reserve_qty": reserve_qty,
                "result": False,
                "sales_org_id": sales_org_id,
                "start_date": start_date,
                "trace_id": trace_id
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def reserve_end(self, start_date, end_date, product_id, reserve_qty, sales_org_id):
        """
        创建赠品活动预占库存
        """
        url = "/central/inventory/reserve/end"
        trace_id = "autotest_reserve_" + str(int(round(time.time() * 1000)))
        data = [
            {
                "comment": "autotest......",
                "end_date": end_date,
                "isMo": 0,
                "product_id": product_id,
                "real_reserve_qty": 0,
                "reserve_qty": reserve_qty,
                "result": False,
                "sales_org_id": sales_org_id,
                "start_date": start_date,
                "trace_id": trace_id
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def special_update(self, sales_org_id, product_id, change_qty, start_time, end_time):
        """
        开启秒杀接口
        """
        url = "/central/inventory/sync/update/special/inv"
        trace_id = "autotest_special_" + str(int(round(time.time() * 1000)))
        data = [
            {
                "batch_id": trace_id,
                "sales_org_id": sales_org_id,
                "products": [
                    {
                        "product_id": product_id,
                        "change_qty": change_qty,
                        "start_time": start_time,
                        "end_time": end_time    # 结束时间小于当前时间秒杀结束
                    }
                ]
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def special_inv(self, product_id, sales_org_id):
        """
        开启秒杀接口
        """
        url = "/central/inventory/query/special/inv"
        data = [
            {
                "products": [
                    {
                        "productId": product_id
                    }
                ],
                "salesOrgId": sales_org_id
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # FPO相关业务接口
    def repair_order_warehouse_date(self):
        """
        修改订单的下发仓库号和配送时间
        """
        url = "/central/inventory/repair/order/warehouse"
        data = [
            {
                "order_id": 42649488,
                "force": True,
                "origin_delivery_date": "2025-04-18",
                "delivery_date": "2025-04-20",
                "product_list": [
                    {
                        "inventory_id": "25",
                        "product_id": 100178
                    }
                ]
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def region_delivery_refresh(self):
        """
        FPO更新region信息同步INV
        """
        url = "/central/inventory/region/delivery/refresh"
        data = []
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def region_item_delivery_refresh(self):
        """
        FPO更新item履约信息同步INV
        """
        url = "/central/inventory/region/item/delivery/refresh"
        data = []
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def region_warehouse_product_cutoff_time(self):
        """
        FPO截单通知INV
        """
        url = "/central/inventory/region/warehouse/product/cutoff/time"
        data = []
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
