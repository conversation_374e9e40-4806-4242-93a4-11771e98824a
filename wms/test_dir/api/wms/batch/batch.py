# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  batch.py
@Description    :  
@CreateTime     :  2023/12/4 10:09
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/12/4 10:09
"""
import weeeTest
from datetime import datetime
from wms.test_dir.api.wms import header


class Batch(weeeTest.TestCase):
    """
    预占接口
    """

    def get_available_stock_list(self, warehouse_number, storage_type, size_id=None, location_no=None, start_column=0,
                                 page_size=10):
        """
        查询可用的stock位
        :param warehouse_number:
        :param storage_type:
        :param size_id:
        :param location_no:
        :param start_column:
        :param page_size:
        :return:
        """
        body = {
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "size_id": size_id,
            "location_no": location_no,
            "startColumn": start_column,
            "pageSize": page_size}

        self.post(url='/wms/service/queryAvaiStockList', headers=header, json=body)
        assert self.response['success'] is True
        return self.response

    def get_available_bin_list(self, warehouse_number, storage_type, size_id=None, location_no=None, start_column=0,
                               page_size=10):
        """
        查询可用的bin位
        :param warehouse_number:
        :param storage_type:
        :param size_id:
        :param location_no:
        :param start_column:
        :param page_size:
        :return:
        """
        body = {
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "size_id": size_id,
            "location_no": location_no,
            "startColumn": start_column,
            "pageSize": page_size
        }

        self.post(url='/wms/service/queryAvaiBinList', headers=header, json=body)
        assert self.response['success'] is True
        return self.response

    def confirm_location_inv(self, location_no, item_number, adjust_number=150, warehouse_number=8):
        """
        添加库存
        """
        body = {
            "item_number": item_number,
            "warehouse_number": warehouse_number,
            "in_user": "yang.li.28(7875714)",
            "reason": "Lost or Found",
            "adjustList": [
                {
                    "location_no": f"{location_no}\t",
                    "adjust_number": adjust_number,
                    "is_lpn": False
                }
            ]
        }
        self.post(url='/wms/adjust/confirmLocationInv', headers=header, json=body)
        assert self.response['success'] is True
        return self.response

    def pre_occupancy_and_batch(self, warehouse_number, order_type=None):
        """
        触发预占
        :return:
        """
        url = f'/wms/location/preoccupancyAndBatch/{warehouse_number}'
        if order_type is not None:
            url = url + f"?order_type={order_type}"
        self.post(url=url, headers=header)
        assert self.response['success'] is True

    def update_config(self, config_key, config_value, warehouse_number, rec_id):
        """
        更新配置
        :param config_key:
        :param config_value:
        :param warehouse_number:
        :param rec_id:
        :return:
        """
        body = {
            "config_key": config_key,
            "config_value": config_value,
            "desc": None,
            "edit_user": "yang.li.28(7875714)",
            "edit_dtm": 1703224366,
            "warehouse_number": warehouse_number,
            "rec_id": rec_id
        }
        self.post(url='/wms/common/updateConfig', headers=header, json=body)
        assert self.response['success'] is True

    def query_order_info(self, warehouse_number, delivery_date, page_size=10):
        """
        搜索订单信息
        :param warehouse_number:
        :param delivery_date:
        :param page_size:
        :return:
        """
        body = {
            "batch_warehouse_number": warehouse_number,
            "carrier_id": "",
            "delivery_dtm": delivery_date,
            "in_dtm_moment": "2023-12-31T16:00:00.000Z",
            "delivery_warehouse_number": warehouse_number,
            "draw": 2,
            "exsd": None,
            "exsd_moment": None,
            "order": {
                "orderColumn": "status",
                "orderRule": "desc"
            },
            "order_number": "",
            "order_wave_id": "",
            "packing_no": "",
            "pageSize": page_size,
            "picker": "",
            "startColumn": 0,
            "status": "",
            "summary_in_dtm_moment": "2023-12-26T16:00:00.000Z",
            "summary_time": "2023-12-27",
            "summary_warehouse_number": warehouse_number,
            "cart_no": "",
            "tote_no": "",
            "urgency": "",
            "warehouse_number": warehouse_number,
            "order_type": "",
            "skus_string": "",
            "inventory_source": None
        }
        self.post(url='/wms/batch/queryPickingOrderList', headers=header, json=body)
        assert self.response['success'] is True

    def manual_orders_batch(self, order_id, order_type, warehouse_number, delivery_date=None, delivery_date_dtm=None):
        """
        手动预占
        :param order_id: wms订单号
        :param delivery_date: 配送日期
        :param order_type: 订单类型
        :param warehouse_number: 仓库号
        :return:
        """
        if not delivery_dtm:
            delivery_dtm = int(datetime.strptime(delivery_date + ' 08:00:00', '%Y-%m-%d %H:%M:%S').timestamp())
        body = {
            "delivery_dtm": None,
            "orderList": [
                {
                    "order_id": order_id,
                    "delivery_dtm": delivery_dtm,
                    "order_type": order_type
                }
            ],
            "warehouse_number": warehouse_number
        }
        self.post(url='/wms/location/orders/batch', headers=header, json=body)
        assert self.response['success'] is True

    def query_order_pool(self, warehouse_number, delivery_date, order_type):
        """
        获取待Batch订单
        """
        url = "/wms/batch/queryPoolOrderInfos"
        body = {
                "box_size": "",
                "delivery_dtm": delivery_date,
                "draw": 2,
                "in_dtm_moment": f"{delivery_date}T16:00:00.000Z",
                "order": None,
                "order_pick_type": "",
                "order_type": order_type,
                "pageSize": 1000,
                "startColumn": 0,
                "status": 1,
                "warehouse_number": warehouse_number
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response["body"]["data"]

    def ready_for_picking(self, delivery_data, region_name, warehouse_num):
        """
        Ready for picking
        :param delivery_data: "2024-01-06"
        :param region_name: "BA-R2"
        :param warehouse_num: "25"
        :return:
        """
        body = {
            "delivery_dtm": delivery_data,
            "region_name": region_name,
            "warehouse_num": warehouse_num
        }
        self.post(url='/wms/outbound/orders/readyForPicking', headers=header, json=body)
        assert self.response['success'] is True

    def get_location_inv_list(self, item_number, warehouse_number='25', location_no="AUTOSTORE"):
        """
        查询库存
        :param item_number:
        :param warehouse_number:
        :param location_no:
        :return:
        """
        body = {
            "warehouse_number": warehouse_number,
            "keyword": "",
            "item_number": item_number,
            "location_no": location_no,
            "location_types": [],
            "order": {
                "orderColumn": "flag",
                "orderRule": "desc"
            },
            "startColumn": 0,
            "pageSize": 10
        }
        self.post(url='/wms/adjust/queryLocationInvList', headers=header, json=body)
        assert self.response['success'] is True
        return self.response
