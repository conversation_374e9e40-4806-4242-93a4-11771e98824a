# !/usr/bin/python3
# -*- coding: utf-8 -*-


from wms.test_dir.api.wms.wms import wms

class DownOrderProcess:
    """订单从down单到生成Batch的一系列操作"""
    def __init__(self):
        pass

    def job_down_order(delivery_date, region_type, region_id):
        """调用job wdown order, 生产环境不可以调用该接口"""
        # 获取Last push time
        config_list = wms.down_order.query_fpo_config(delivery_date, region_type, region_id)
        assert config_list, f'delivery_date={delivery_date},region_type={region_type}, region_id={region_id}没有找到FPO config'
        last_push_time = config_list[0]["last_push_time"]
        wms.down_order_job.JobDownOrder(last_push_time)
        wms.util.wait(sleep_time=10)

    def manual_pull_order(warehouse_number, delivery_date, region_ids, user_id):
        """手动Pull Order"""
        # 获取Last push time
        wms.down_order.pull_order_from_so()
        wms.down_order.invoice_generate(delivery_date, region_ids, warehouse_number, user_id)
        wms.util.wait(sleep_time=10)

    def cutoff_order(self, warehouse_number, delivery_date, region_id):
        """截单"""
        wms.down_order_job.cutoff_config(delivery_date, region_id, warehouse_number)

    def ready_for_picking(self, warehouse_number, delivery_date, region_name):
        """
        ready_for_picking
        """
        wms.down_order.ready_for_picking(warehouse_number, delivery_date, region_name)

    def generate_batch_operate(warehouse_number, delivery_date, order_type):
        """订单预占操作"""
        # 检查订单下商品库存是否可预占，不够则添加库存
        
        # 修改系统日期
        config_keys = ["wms:warehouse:sys_date", "wms:order:delivery_date"]
        for config_key in config_keys:
            wms.update_central_config(warehouse_number, config_key, delivery_date)
        # 预占
        wms.batch.pre_occupancy_and_batch(warehouse_number, order_type)
        # 查询是否有未Batch成功的订单
        pool_order_list = wms.batch.query_order_pool(warehouse_number, delivery_date, order_type)
        for order in pool_order_list:
            wms.batch.manual_orders_batch(order_id=order["order_id"], delivery_dtm=order["delivery_dtm"],
                                            order_type=order["order_type"],
                                            warehouse_number=order["warehouse_number"])